import React, { use<PERSON>emo, useState } from "react";
import { Box, Button, Popover, Typo<PERSON>, <PERSON><PERSON>ield, IconButton, Tooltip } from "@mui/material";
import { ChromePicker, ColorResult } from "react-color";
import { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from "../../../assets/icons/icons";
import useDrawerStore, { ButtonContainer, TButton } from "../../../store/drawerStore";
import AddIcon from "@mui/icons-material/Add";
import ButtonSetting from "./ButtonSetting";
import { useAsyncError } from "react-router-dom";
import { useTranslation } from 'react-i18next';

const ButtonSection: React.FC<{ items: ButtonContainer; updatedGuideData: any; isCloneDisabled?: boolean }> = ({
	items: buttonsContainer,
	updatedGuideData,
	isCloneDisabled,
}) => {
	const { t: translate } = useTranslation();
	const {
		// buttonsContainer,
		tooltipBtnSettingAnchorEl: settingAnchorEl,
		cloneTooltipButtonContainer: cloneButtonContainer,
		updateButtonInTooltip: updateButton,
		addNewButtonInTooltip: addNewButton,
		deleteButtonInTooltip: deleteButton,
		updateTooltipButtonInteraction,
		updateTooltipButtonAction,
		deleteTooltipButtonContainer: deleteButtonContainer,
		updateTooltipBtnContainer: updateContainer,
		setTooltipBtnSettingAnchorEl: setSettingAnchorEl,
		currentStep,
		setbtnidss,
		getCurrentButtonInfo,
		toolTipGuideMetaData,
		highlightedButton,
		setElementClick,
		SetElementButtonClick,
	} = useDrawerStore((state: any) => state);
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);
	const [isDeleteIcon, setIsDeleteIcon] = useState("");
	const [currentContainerId, setCurrentContainerId] = useState("");
	const [currentButtonId, setCurrentButtonId] = useState("");
	// Default button color
	let clickTimeout: NodeJS.Timeout;
	const handleClick = (event: React.MouseEvent<HTMLElement>, containerId: string, buttonId: string) => {
		const target = event.currentTarget;
		setAnchorEl(target);
		setSettingAnchorEl({
			containerId,
			buttonId,
			value: null,
		});

		// Set current container and button IDs for reference
		setCurrentContainerId(containerId);
		setCurrentButtonId(buttonId);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {
		setColorPickerAnchorEl(event.currentTarget);
	};

	const handleColorChange = (color: ColorResult) => {
		// Update the backgroundColor in the container's style
		updateContainer(settingAnchorEl.containerId, "style", {
			backgroundColor: color.hex,
		});

		// Also update the BackgroundColor property at the ButtonSection level
		updateContainer(settingAnchorEl.containerId, "BackgroundColor", color.hex);
	};

	const handleCloseColorPicker = () => {
		setColorPickerAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	// const open = Boolean(anchorEl && !isEditingButton);
	// const id = open ? "button-popover" : undefined;
	const colorPickerOpen = Boolean(colorPickerAnchorEl);

	const handleEditButtonName = (
		containerId: string,
		buttonId: string,
		isEditing: keyof TButton,
		value: TButton[keyof TButton]
	) => {
		// clearTimeout(clickTimeout);
		updateButton(containerId, buttonId, isEditing, value);
	};

	const handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {
		updateButton(containerId, buttonId, "type", value);
		setAnchorEl(null);
	};

	const handleAddIconClick = (containerId: string) => {
		addNewButton(containerId);
	};

	const shouldShowAddBtn = buttonsContainer.buttons.length;

	const buttonInfo = useMemo(() => {
		let result = null;
		if (settingAnchorEl.buttonId) {
			result = buttonsContainer.buttons.find((item: any) => item.id === settingAnchorEl.buttonId);
		}
		return result;
	}, [settingAnchorEl.buttonId, buttonsContainer.buttons]);

	// console.log({ buttonInfo });

	// setButtonId(currentButtonId);
	// setCuntainerId(currentButtonId);
	const handleDelteContainer = () => {
		deleteButtonContainer(settingAnchorEl.containerId);
		setElementClick("element");
		const updatedData = { ...updatedGuideData };
		if (updatedData.GuideStep && updatedData.GuideStep[currentStep]) {
			const stepData = { ...updatedData.GuideStep[currentStep - 1] };

			// Ensure GotoNext exists before modifying ButtonId
			if (stepData.Design && stepData.Design.GotoNext && stepData.Design.GotoNext.ButtonId !== undefined) {
				stepData.Design = {
					...stepData.Design,
					GotoNext: {
						...stepData.Design.GotoNext,
						ButtonId: "",
						NextStep: "element",
					},
				};
			}
			setbtnidss("");
			// Update the GuideStep array with modified step data
			updatedData.GuideStep = [...updatedData.GuideStep];
			updatedData.GuideStep[currentStep - 1] = stepData;
			updatedGuideData = updatedData;
		}

		setbtnidss("");
		setAnchorEl(null);
	};

	const handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {
		//current container and button IDs
		const containerId = settingAnchorEl.containerId || currentContainerId;
		const buttonId = settingAnchorEl.buttonId || currentButtonId;

		setSettingAnchorEl({
			containerId,
			buttonId,
			// @ts-ignore
			value: event.currentTarget,
		});
		handleClose();
	};

	const handleCloseSettingPopup = (_containerId: string, _buttonId: string) => {
		// updateButtonAction(containerId, buttonId, {
		// 	value: selectedActions,
		// 	targetURL: targetURL,
		// 	tab: selectedTab,
		// 	interaction: null,
		// });
		// updateButtonInteraction(containerId, buttonId, selectedInteraction);
		setSettingAnchorEl({
			containerId: "",
			buttonId: "",
			value: null,
		});
		setAnchorEl(null);
	};

	// Clear the design button settings if the deleted button was previously selected
	const deletebuttonidindesign = (buttonid: any, deletebuttonid: any) => {
		if (buttonid === deletebuttonid) {
			const targetStep = { ...updatedGuideData?.GuideStep?.[currentStep - 1] };
			if (targetStep && targetStep.Design) {
				targetStep.Design.GotoNext = {
					ButtonId: "",
					ButtonName: "",
					ElementPath: "",
					NextStep: "element",
				};
			}
			updatedGuideData.GuideStep[currentStep - 1] = targetStep;
		}
	};


	const handleApplyChanges = (
		tempColors: any, // Changed from string to any to handle the object structure
		selectedActions: string,
		targetURL: string,
		selectedInteraction: string,
		currentButtonName: string,
		selectedTab: string
	) => {
		// Get the container and button IDs
		const { containerId, buttonId } = settingAnchorEl;
		// Update the button style - make sure we're passing the correct structure
		updateButton(containerId, buttonId, "style", {
			backgroundColor: tempColors.backgroundColor,
			borderColor: tempColors.borderColor,
			color: tempColors.color
		});
		updateTooltipButtonAction(containerId, buttonId, {
			value: selectedActions,
			targetURL: targetURL,
			tab: selectedTab,
			interaction: null,
		});
		updateTooltipButtonInteraction(containerId, buttonId, selectedInteraction);
		updateButton(containerId, buttonId, "name", currentButtonName);

		// Clear selection
		setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
		setAnchorEl(null);
	};

	return (
		<>
			<Box
				component={"div"}
				id={buttonsContainer.id}
				sx={{
					height: "60px",
					width: "100%",
					display: "flex",
					alignItems: "center",
					gap: "5px",
					padding: "0px",
					boxSizing: "border-box",
					backgroundColor: buttonsContainer.style.backgroundColor,
					justifyContent: "center",
				}}
				// onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}
				// onMouseLeave={(e) => setCurrentContainerId("")}
			>
				{buttonsContainer.buttons.map((item: any) => {
					return (
						<Box
							sx={{
								position: "relative",
								display: "flex",
								// flex: 1,
								justifyContent: `center`,
								// "&:hover .edit-icon": { display: "inline-flex" },
							}}
							onMouseLeave={() => {
								setIsDeleteIcon("");
							}}
						>
							<Button
								// contentEditable={item.isEditing}
								onMouseOver={(e) => {
									if (item.isEditing === false && e.currentTarget.id === item.id) {
										setIsDeleteIcon(item.id);
									}
								}}
								id={item.id}
								variant={"contained"}
								sx={{
									borderRadius: "8px",
									transition: "none",
									boxShadow: "none !important",
									border: item.style.borderColor,
									//border: `${item.type !== "primary" ? item.style.borderColor : "none"}`,
									color: `${item.style.color}`,
									textTransform: "none",
									padding: "4px 8px !important",
									lineHeight: "var(--button-lineheight)",
									fontSize: "14px !important",
									backgroundColor: item.style.backgroundColor,
									width: "fit-content",
									//boxShadow: "none !important", // Remove box shadow in normal state
									"&:hover": {
										backgroundColor: item.style.backgroundColor, // Keep the same background color on hover
										opacity: 0.9, // Slightly reduce opacity on hover for visual feedback
										boxShadow: "none !important", // Remove box shadow in hover state
									},
								}}
								onClick={(e) => handleClick(e, buttonsContainer.id, item.id)} // Open popover when clicking the button
							>
								{item.name}
							</Button>

							{buttonsContainer.buttons.length > 1 && isDeleteIcon === item.id ? (
								<IconButton
									size="small"
									className="del-icon"
									sx={{
										position: "absolute",
										top: "-10px",
										right: "-10px",
										backgroundColor: "#fff",
										//boxShadow: "none !important", // Remove box shadow in normal state
										// display: "none", // Initially hidden
										boxShadow: "rgba(0, 0, 0, 0.4) 0px 2px 6px",
										zIndex: "1",
										padding : "3px !important",
										"&:hover": {
											backgroundColor: "#fff",
											boxShadow: "none !important", 
										},
										span: {
											height: "14px"
										},
										svg: {
											width: "14px", 
											height: "14px", 
											path: {
												fill:"#ff0000"
											}
										},
									}}
									onClick={(e) => {
										e.stopPropagation();
										deletebuttonidindesign(item.id, toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId);
										deleteButton(
											item.id,
											buttonsContainer.id,
											toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId
										);
										setElementClick("element");
										// setButtonClick(false);
										SetElementButtonClick(false);
									}}
								>
									<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
								</IconButton>
							) : null}
						</Box>
					);
				})}
				{shouldShowAddBtn < 4 ? (
					<IconButton
						sx={{
							backgroundColor: "#5F9EA0",
							cursor: "pointer",
							zIndex: 1000,
							padding: "6px !important",
							boxShadow: "none !important", // Remove box shadow in normal state
							"&:hover": {
								backgroundColor: "#70afaf",
								boxShadow: "none !important", // Remove box shadow in hover state
							},
						}}
						// sx={sideAddButtonStyle}
						onClick={() => handleAddIconClick(buttonsContainer.id)}
					>
						<AddIcon
							fontSize="small"
							sx={{ color: "#fff" }}
						/>
					</IconButton>
				) : null}

				<Popover
					id={"button-toolbar"}
					open={open}
					// anchorEl={anchorEl}
					onClose={handleClose}
					anchorReference="anchorPosition"
					anchorPosition={{
						top: anchorEl?.getBoundingClientRect().top || 0,
						left: anchorEl?.getBoundingClientRect().left || 0,
					}}
					anchorOrigin={{
						vertical: "top",
						horizontal: "left",
					}}
					transformOrigin={{
						vertical: "bottom",
						horizontal: "left",
					}}
					slotProps={{
						root: {
							// instead of writing sx on popover write here it also target to root and more clear
							sx: {
								zIndex: (theme) => theme.zIndex.tooltip + 1000,
							},
						},
					}}
				>
					<Box
						sx={{
							display: "flex",
							alignItems: "center",
							gap: "6px",
							padding: "4px",
						}}
					>
						{/*	<Typography
							variant="body2"
							sx={{ cursor: "pointer", fontWeight: "bold" }}
							component={"div"}
							id="primary"
							onClick={(e) =>
								handleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)
							}
						>
							Primary
						</Typography>
						<Typography
							variant="body2"
							sx={{ cursor: "pointer", color: "gray" }}
							component={"div"}
							id="secondary"
							onClick={(e) =>
								handleChangeButton(settingAnchorEl.containerId, settingAnchorEl.buttonId, e.currentTarget.id)
							}
						>
							Secondary
						</Typography>

						<Box sx={{ borderLeft: "1px solid #ccc", height: "24px", marginLeft: "8px" }}></Box>
                       */}
						{/* Icons for additional options */}
						<Tooltip title="Settings" style={{ zIndex: 99999 }}>
						<IconButton
							size="small"
							onClick={handleSettingIconClick}
							sx={{
								boxShadow: "none !important", // Remove box shadow in normal state
								"&:hover": {
									boxShadow: "none !important", // Remove box shadow in hover state
								},
							}}
						>
							<span dangerouslySetInnerHTML={{ __html: settingsicon }}
								style={{width:"20px", height:"20px"}}
							/>
							</IconButton>
						</Tooltip>
						<Tooltip
							title="Background Color"
							style={{ zIndex: 99999 }}
						>
							<Box
								sx={{
									backgroundColor:
										buttonsContainer.style.backgroundColor === "#5f9ea0"
											? buttonsContainer.style.backgroundColor
											: "#e0dbdb",
									width: "20px",
									height: "20px",
									borderRadius: "50%",
									//border: `1px solid red`,
									marginTop: "-3px",
								}}
								component={"div"}
								role="button"
								onClick={handleBackgroundColorClick}
							/>
						</Tooltip>
						<Tooltip
							title={isCloneDisabled ? "Maximum limit of 3 Button sections reached" : "Clone Section"}
							style={{ zIndex: 99999 }}
						>
						<IconButton
							size="small"
							onClick={() => {
								cloneButtonContainer(settingAnchorEl.containerId);
								setAnchorEl(null);
							}}
								disabled={isCloneDisabled}
							sx={{
								boxShadow: "none !important", // Remove box shadow in normal state
								"&:hover": {
									boxShadow: "none !important", // Remove box shadow in hover state
								},
							}}
						>
							<span
								dangerouslySetInnerHTML={{ __html: copyicon }}
								style={{ opacity: isCloneDisabled ? 0.5 : 1, width:"20px", height:"20px" }}
							/>
							</IconButton>
						</Tooltip>
						<Tooltip title="Delete Button Section" style={{ zIndex: 99999 }}>
						<IconButton
							size="small"
							// disabled={buttonsContainer.buttons.length === 1}
							onClick={handleDelteContainer}
							disabled={toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1}
							sx={{
								boxShadow: "none !important", // Remove box shadow in normal state
								"&:hover": {
									boxShadow: "none !important", // Remove box shadow in hover state
								},
							}}
						>
							<span
								dangerouslySetInnerHTML={{ __html: deleteicon }}
								style={{
									opacity: toolTipGuideMetaData[currentStep - 1]?.containers?.length === 1 ? 0.5 : 1,
									pointerEvents: "none",
									width: "20px",
									height: "24px"
								}}
							/>
							</IconButton>
						</Tooltip>
					</Box>
				</Popover>

				<ButtonSetting
					handleCloseSettingPopup={handleCloseSettingPopup}
					settingAnchorEl={settingAnchorEl}
					buttonInfo={buttonInfo}
					handleApplyChanges={handleApplyChanges}
					updatedGuideData={updatedGuideData}
				/>
			</Box>

			{/* Color Picker Popover */}
			<Popover
				open={colorPickerOpen}
				anchorEl={colorPickerAnchorEl}
				onClose={handleCloseColorPicker}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
				id="color-picker"
				slotProps={{
					root: {
						// instead of writing sx on popover write here it also target to root and more clear
						sx: {
							zIndex: (theme) => theme.zIndex.tooltip + 1000,
						},
					},
				}}
			>
				<Box>
					<ChromePicker
						color={buttonsContainer.style.backgroundColor}
						onChange={handleColorChange}
					/>
					<style>
						{`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
					</style>
				</Box>
			</Popover>
		</>
	);
};

export default ButtonSection;
