{"ast": null, "code": "import React,{useEffect,useState}from\"react\";// import Draggable from \"react-draggable\";\nimport{<PERSON><PERSON>,Box,Typography,IconButton,Select,MenuItem}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import DesignServicesIcon from\"@mui/icons-material/DesignServices\";import CanvasSettings from\"./CanvasSettings\";import Elementssettings from\"../guideSetting/ElementsSettings\";import OverlaySettings from\"./Overlay\";import CustomCSS from\"./CustomCss\";import PageInteractions from\"../guideBanners/selectedpopupfields/PageInteraction\";import AnimationSettings from\"./Animation\";import\"./Canvas.module.css\";import useDrawerStore from\"../../store/drawerStore\";import HotspotSettings from\"../hotspot/HotspotSettings\";import{animation,elements,Hotspoticon,overlay,Reselect,designicon}from\"../../assets/icons/icons\";import TooltipCanvasSettings from\"../Tooltips/designFields/TooltipCanvasSettings\";import{KeyboardTabSharp}from\"@mui/icons-material\";import{GetGudeDetailsByGuideId}from\"../../services/GuideListServices\";import userSession from\"../../store/userSession\";import ChecklistCanvasSettings from\"../checklist/ChecklistCanvasSettings\";import LauncherSettings from\"../checklist/LauncherSettings\";import Checkpoints from\"../checklist/Chekpoints\";import TitleSubTitle from\"../checklist/TitleSubTitle\";import'../../styles/rtl_styles.scss';import{useTranslation}from'react-i18next';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const DesignMenu=_ref=>{let{width,height,overlays,setOverLays,backgroundC,setBackgroundC,Bposition,setBposition,bpadding,setbPadding,Bbordercolor,setBBorderColor,BborderSize,setBBorderSize,zindeex,setZindeex,setDesignPopup,selectedTemplate,designPopup,initialGuideData,updatedGuideData,handleSaveGuide,resetHeightofBanner}=_ref;const{t:translate}=useTranslation();// State to control the visibility of CanvasSettings\nconst[showCanvasSettings,setShowCanvasSettings]=useState(false);const[showChecklistCanvasSettings,setShowChecklistCanvasSettings]=useState(false);//const [showTooltipCanvasSettings, setShowTooltipCanvasSettings] = useState(false);\nconst[showOverlay,setOverlaySettings]=useState(false);const[showElementsSettings,setShowElementsSettings]=useState(false);const[showAnimation,setshowAnimation]=useState(false);const[showCustomCSS,setShowCustomCSS]=useState(false);const[anchorEl,setAnchorEl]=useState(null);const[reselectElement,setReselectElement]=useState(false);const[goToNextElement,setGoToNextElement]=useState(false);// const [elementClick, setElementClick] = useState(false);\n//const [dropdownValue, setDropdownValue] = useState(\"\");\nconst[isOpen,setIsOpen]=useState(true);const{setCurrentGuideId,currentGuideId,getCurrentGuideId}=userSession(state=>state);const{//selectedTemplate,\npadding,setPadding,position,setPosition,radius,setRadius,borderSize,setBorderSize,setBorderColor,borderColor,setBackgroundColor,backgroundColor,overlayEnabled,setOverlayEnabled,setZiindex,setguidesSettingspopup,setHotspotPopup,setTitlePopup,titlePopup,hotspotPopup,showTooltipCanvasSettings,setShowTooltipCanvasSettings,setTooltipBackgroundcolor,setTooltipBordercolor,setTooltipBorderradius,setTooltipBordersize,CANVAS_DEFAULT_VALUE,savedGuideData,ButtonsDropdown,setButtonsDropdown,elementSelected,setElementSelected,currentHoveredElement,elementClick,setElementClick,elementButtonName,setElementButtonName,updateDesignelementInTooltip,toolTipGuideMetaData,elementbuttonClick,SetElementButtonClick,buttonClick,setButtonClick,currentStep,highlightedButton,setHighlightedButton,setSelectActions,updateTooltipButtonAction,mapButtonSection,btnidss,selectedTemplateTour,progress,setProgress,setSelectedOption,dropdownValue,setDropdownValue,setIsUnSavedChanges,showLauncherSettings,setShowLauncherSettings,checkpointsPopup,setCheckPointsPopup,createWithAI,interactionData}=useDrawerStore(state=>state);const setbtnidss=useDrawerStore(state=>state.setbtnidss);useEffect(()=>{setShowCanvasSettings(false);setShowChecklistCanvasSettings(false);setShowTooltipCanvasSettings(false);setOverlaySettings(false);setShowElementsSettings(false);setshowAnimation(false);setShowCustomCSS(false);setHotspotPopup(false);setTitlePopup(false);setShowLauncherSettings(false);},[selectedTemplate,selectedTemplateTour]);// const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\n// const setOverlayEnabled = useDrawerStore((state) => state.setOverlayEnabled);\nconst toggleCanvasSettings=()=>{if(selectedTemplate===\"Tooltip\"||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\"){setShowTooltipCanvasSettings(!showTooltipCanvasSettings);}else if(selectedTemplate===\"Checklist\"){setShowChecklistCanvasSettings(!showChecklistCanvasSettings);}else{setShowCanvasSettings(!showCanvasSettings);}};const toggleOverlaySettings=()=>{setMenuPopup(false);setOverlaySettings(!showOverlay);};const handleHotspotClick=()=>{//setguidesSettingspopup(false); // Close any other popups\nsetHotspotPopup(true);// Open the hotspot popup\nsetTimeout(()=>{setHotspotPopup(true);// Ensure the popup is rendered\n},0);};const handleTitlePopup=()=>{setTitlePopup(true);};const handleCheckPointPopup=()=>{setCheckPointsPopup(true);};// useEffect(() => {\n// \tsetTimeout(() => {\n// \t\tsetHotspotPopup(true); // Ensure the popup is rendered\n// \t}, 0);\n// }, [hotspotPopup]);\n// Removed useEffect that was resetting dropdownValue on currentStep change\n// This was causing the dropdown to show \"Select an option\" even after selection\nconst toggleReselectElement=()=>{//setReselectElement(!reselectElement);\n//setTooltipXaxis(\"4\");\n//setTooltipYaxis(\"4\");\n//setTooltipPosition(\"middle-center\");\n//setTooltipBackgroundcolor(\"\");\n//setTooltipBordercolor(\"\");\nsetTooltipBorderradius(\"8\");//setTooltipBordersize(\"1\");\n//setTooltipPadding(\"4\");\n//setTooltipWidth(\"400\");\n//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\nsetElementSelected(true);setIsTooltipPopup(false);setShowTooltipCanvasSettings(false);};useEffect(()=>{const fetchGuideDetails=async()=>{if(currentGuideId!=\"\"&&currentGuideId!=null){var _tooltipMetadata$desi;// First, check the current toolTipGuideMetaData for the most up-to-date state\nconst tooltipMetadata=toolTipGuideMetaData===null||toolTipGuideMetaData===void 0?void 0:toolTipGuideMetaData[currentStep-1];if(tooltipMetadata!==null&&tooltipMetadata!==void 0&&(_tooltipMetadata$desi=tooltipMetadata.design)!==null&&_tooltipMetadata$desi!==void 0&&_tooltipMetadata$desi.gotoNext){// Use the current metadata as the source of truth\nconst hasButtonClick=tooltipMetadata.design.gotoNext.ButtonId&&tooltipMetadata.design.gotoNext.ButtonId.trim()!==\"\";console.log(\"useEffect: Has button click:\",hasButtonClick,\"ButtonId:\",tooltipMetadata.design.gotoNext.ButtonId);if(hasButtonClick){setElementClick(\"button\");setButtonClick(true);SetElementButtonClick(true);// Use ButtonId for dropdown value, not ButtonName\nsetDropdownValue(tooltipMetadata.design.gotoNext.ButtonId||\"\");setElementButtonName(tooltipMetadata.design.gotoNext.ButtonName||tooltipMetadata.design.gotoNext.buttonName||\"\");setbtnidss(tooltipMetadata.design.gotoNext.ButtonId||\"\");console.log(\"useEffect: Set button click mode with values:\",{elementClick:\"button\",buttonClick:true,dropdownValue:tooltipMetadata.design.gotoNext.ButtonId,elementButtonName:tooltipMetadata.design.gotoNext.ButtonName,btnidss:tooltipMetadata.design.gotoNext.ButtonId});}else{setElementClick(\"element\");setButtonClick(false);SetElementButtonClick(false);setDropdownValue(\"\");setElementButtonName(\"\");setbtnidss(\"\");console.log(\"useEffect: Set element click mode\");}}else{var _data$GuideDetails,_data$GuideDetails$Gu,_guideStep$Design;// Fallback to fetching from database if metadata doesn't exist\nconst data=await GetGudeDetailsByGuideId(currentGuideId,createWithAI,interactionData);const guideStep=data===null||data===void 0?void 0:(_data$GuideDetails=data.GuideDetails)===null||_data$GuideDetails===void 0?void 0:(_data$GuideDetails$Gu=_data$GuideDetails.GuideStep)===null||_data$GuideDetails$Gu===void 0?void 0:_data$GuideDetails$Gu[currentStep-1];if(guideStep!==null&&guideStep!==void 0&&(_guideStep$Design=guideStep.Design)!==null&&_guideStep$Design!==void 0&&_guideStep$Design.GotoNext){const hasButtonClick=guideStep.Design.GotoNext.ButtonId&&guideStep.Design.GotoNext.ButtonId.trim()!==\"\";if(hasButtonClick){setElementClick(\"button\");setButtonClick(true);SetElementButtonClick(true);// Use ButtonId for dropdown value, not ButtonName\nsetDropdownValue(guideStep.Design.GotoNext.ButtonId||\"\");setElementButtonName(guideStep.Design.GotoNext.ButtonName||\"\");setbtnidss(guideStep.Design.GotoNext.ButtonId||\"\");}else{setElementClick(\"element\");setButtonClick(false);SetElementButtonClick(false);setDropdownValue(\"\");setElementButtonName(\"\");setbtnidss(\"\");}}else{setElementClick(\"element\");setButtonClick(false);SetElementButtonClick(false);setDropdownValue(\"\");setElementButtonName(\"\");setbtnidss(\"\");}}}};fetchGuideDetails();},[currentStep,toolTipGuideMetaData]);const removeAppliedStyleOfEle=element=>{element.removeAttribute(\"disabled\");element.style.outline=\"\";element.style.pointerEvents=\"unset\";};const[menuPopup,setMenuPopup]=useState(true);const onReselectElement=()=>{// setTooltipXaxis(\"4\");\n// setTooltipYaxis(\"4\");\n// setTooltipPosition(\"middle-center\");\n// setTooltipBackgroundcolor(\"\");\n// setTooltipBordercolor(\"\");\n// setTooltipBorderradius(\"4\");\n// setTooltipBordersize(\"1\");\n// setTooltipPadding(\"4\");\n// setTooltipWidth(\"400\");\n//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n//setElementSelected(true);\n// setElementSelected(false);\n// setIsTooltipPopup(false);\n// setShowTooltipCanvasSettings(false);\nconst existingHotspot=document.getElementById(\"hotspotBlinkCreation\");const existingTooltip=document.getElementById(\"Tooltip-unique\");setIsUnSavedChanges(false);// existingTooltip.remove();\nsetDesignPopup(false);setElementSelected(false);currentHoveredElement&&removeAppliedStyleOfEle(currentHoveredElement);setIsUnSavedChanges(true);};// const [highlightedButton, setHighlightedButton] = useState(null);\n// Function to handle button highlighting\nconst handleButtonClick=buttonId=>{console.log(\"Button clicked:\",buttonId);setIsUnSavedChanges(true);handleToggleClick(buttonId);setHighlightedButton(prev=>prev===buttonId?null:buttonId);// Remove immediate save to prevent state interference\n// handleSaveGuide();\n};const handleLaunchSettings=()=>{setShowLauncherSettings(true);};const handleToggleClick=type=>{console.log(\"handleToggleClick called with type:\",type);if(type===1){// Switching to \"element click\"\nconsole.log(\"Switching to element click mode\");setElementClick(\"element\");setElementButtonName(\"\");setDropdownValue(\"\");setbtnidss(\"\");setButtonClick(false);SetElementButtonClick(false);// Update the gotoNext object to reflect \"element click\" state\nconst updatedCanvasSettings={NextStep:\"element\",ButtonId:\"\",ElementPath:\"\",ButtonName:\"\",Id:\"\"};updateDesignelementInTooltip(updatedCanvasSettings);}else if(type===2){var _tooltipMetadata$desi2,_tooltipMetadata$desi3;// Switching to \"button click\"\nconsole.log(\"Switching to button click mode\");setElementClick(\"button\");setButtonClick(true);SetElementButtonClick(true);// CRITICAL FIX: When switching to button click, restore the dropdown state from metadata\nconst tooltipMetadata=toolTipGuideMetaData===null||toolTipGuideMetaData===void 0?void 0:toolTipGuideMetaData[currentStep-1];console.log(\"Current tooltip metadata:\",tooltipMetadata);if(tooltipMetadata!==null&&tooltipMetadata!==void 0&&(_tooltipMetadata$desi2=tooltipMetadata.design)!==null&&_tooltipMetadata$desi2!==void 0&&(_tooltipMetadata$desi3=_tooltipMetadata$desi2.gotoNext)!==null&&_tooltipMetadata$desi3!==void 0&&_tooltipMetadata$desi3.ButtonId){const buttonId=tooltipMetadata.design.gotoNext.ButtonId;const buttonName=tooltipMetadata.design.gotoNext.ButtonName;// Update all related state variables to ensure dropdown shows correctly\nsetDropdownValue(buttonId);setElementButtonName(buttonName||\"\");setbtnidss(buttonId);console.log(\"Restored button click state when switching to button mode:\",{buttonId,buttonName,dropdownValue:buttonId});}else{console.log(\"No existing button click data found in metadata\");}}};const DesignelementInTooltip=(value,name)=>{const updatedCanvasSettings={NextStep:elementClick,ButtonId:value,ElementPath:\"\",ButtonName:name===null||name===void 0?void 0:name.name,Id:value// Add the Id property to match what's expected in Tooltips.tsx\n};updateDesignelementInTooltip(updatedCanvasSettings);};const handleDropdownChange=event=>{var _toolTipGuideMetaData,_toolTipGuideMetaData2,_buttonContainer$butt;const selectedValue=event.target.value;console.log(\"Dropdown changed to:\",selectedValue);// Find the button container dynamically instead of using hardcoded index\nconst buttonContainer=(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData2=_toolTipGuideMetaData.containers)===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.find(container=>container.type===\"button\");const selectedButton=buttonContainer===null||buttonContainer===void 0?void 0:(_buttonContainer$butt=buttonContainer.buttons)===null||_buttonContainer$butt===void 0?void 0:_buttonContainer$butt.find(button=>button.id===selectedValue);console.log(\"Selected button:\",selectedButton);console.log(\"Button container:\",buttonContainer);// Update all relevant state variables\nsetDropdownValue(selectedValue);setElementButtonName((selectedButton===null||selectedButton===void 0?void 0:selectedButton.name)||selectedValue);// Use button name, fallback to ID\nsetbtnidss(selectedValue);setElementClick(\"button\");// Update the design metadata with both ID and name for proper persistence\nDesignelementInTooltip(selectedValue,selectedButton);// Mark as unsaved changes\nsetIsUnSavedChanges(true);console.log(\"Updated state after dropdown change:\",{dropdownValue:selectedValue,elementButtonName:(selectedButton===null||selectedButton===void 0?void 0:selectedButton.name)||selectedValue,btnidss:selectedValue,elementClick:\"button\"});};const toggleElementsSettings=()=>{setMenuPopup(false);setShowElementsSettings(true);//setDesignPopup(false);\nreturn/*#__PURE__*/_jsxs(_Fragment,{children:[showElementsSettings&&designPopup&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Elementssettings,{setShowElementsSettings:setShowElementsSettings,setDesignPopup:setDesignPopup})}),\";\"]});};const toggleCustomCSS=()=>{setShowCustomCSS(!showCustomCSS);// Toggle CustomCSS visibility\n};const toggleAnimation=()=>{setshowAnimation(!showAnimation);// Toggle CustomCSS visibility\n};const handleClose=()=>{setIsOpen(false);// Close the popup when close button is clicked\nsetDesignPopup(false);};const handleDismissDataChange=data=>{};if(!isOpen)return null;const handleStatusChange=status=>{setOverlayEnabled(status);};return/*#__PURE__*/(//<Draggable>\n_jsxs(\"div\",{id:\"qadpt-designpopup\",className:\"qadpt-designpopup\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-design-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-title\",children:translate(\"Design\")}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",\"aria-label\":translate(\"close\"),onClick:handleClose,children:/*#__PURE__*/_jsx(CloseIcon,{})})]}),titlePopup&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(TitleSubTitle,{})}),showLauncherSettings&&/*#__PURE__*/_jsx(LauncherSettings,{}),menuPopup&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-controls\",children:[(selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\")&&/*#__PURE__*/_jsxs(Box,{className:\" qadpt-control-box\",onClick:onReselectElement,sx:{cursor:\"pointer\"},children:[/*#__PURE__*/_jsx(Typography,{className:\"qadpt-control-label\",sx:{color:\"#495e58\"},children:translate(\"Reselect Element\")}),/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-reselect-icon\",dangerouslySetInnerHTML:{__html:Reselect},style:{padding:\"5px\",marginRight:\"10px\"}})]}),(selectedTemplate===\"Tooltip\"||selectedTemplateTour===\"Tooltip\")&&/*#__PURE__*/_jsxs(\"div\",{// className=\"qadpt-design-btn\"\nstyle:{marginBottom:\"8px\",borderRadius:\"12px\",padding:\"8px 12px\",background:\"#eae2e2\",textTransform:\"none\"},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",justifyContent:\"flex-start\",alignItems:\"center\"},className:\"qadpt-gtnext\",children:[/*#__PURE__*/_jsx(\"span\",{style:{background:\"rgba(95, 158, 160, 0.2)\",borderRadius:\"100px\",padding:\"4px 8px\"},children:/*#__PURE__*/_jsx(KeyboardTabSharp,{style:{color:\"var(--primarycolor)\",height:\"21px\",width:\"21px\"// borderRadius: \"50px\",\n// background: \"rgba(95, 158, 160, 0.2)\",\n}})}),/*#__PURE__*/_jsx(Typography,{sx:{color:\"#444444\",fontWeight:\"600\"},children:translate(\"Go to next step\")})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",gap:\"6px\",marginTop:\"10px\"},children:[/*#__PURE__*/_jsx(\"div\",{onClick:()=>handleButtonClick(1),style:{display:\"flex\",justifyContent:\"flex-start\",alignItems:\"center\",height:\"40px\",borderRadius:\"8px\",cursor:\"pointer\",backgroundColor:elementClick===\"element\"?\"rgba(95, 158, 160, 0.2)\":\"rgb(196, 193, 193, 0.3)\",border:elementClick===\"element\"?\"1px solid var(--primarycolor)\":\"1px solid transparent\",width:\"95px\"},children:/*#__PURE__*/_jsx(Typography,{sx:{color:\"#1c1b1f\",padding:\"0 6px\",fontSize:\"12px !important\"},children:translate(\"Element Click\")})}),/*#__PURE__*/_jsx(\"div\",{onClick:()=>handleButtonClick(2),style:{display:\"flex\",justifyContent:\"flex-start\",alignItems:\"center\",height:\"40px\",borderRadius:\"8px\",cursor:\"pointer\",backgroundColor:elementClick!==\"element\"?\"rgba(95, 158, 160, 0.2)\":\"rgb(196, 193, 193, 0.3)\",border:elementClick!==\"element\"?\"1px solid var(--primarycolor)\":\"1px solid transparent\",width:\"95px\"},children:/*#__PURE__*/_jsx(Typography,{sx:{color:\"#1c1b1f\",padding:\"0 11px\",fontSize:\"12px !important\"},children:translate(\"Button Click\")})})]}),buttonClick&&/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(Box,{className:\"qadpt-chos-btn\",sx:{margin:\"0 !important\",marginBottom:\"8px\",borderRadius:\"12px\",background:\"#eae2e2\",textTransform:\"none\"},children:[/*#__PURE__*/_jsx(Typography,{sx:{padding:\"4px\",color:\"#495e58a\"},children:translate(\"Choose Button\")}),((_toolTipGuideMetaData3,_toolTipGuideMetaData4)=>{// Find the button container dynamically instead of using hardcoded index\nconst buttonContainer=toolTipGuideMetaData===null||toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData3=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData3===void 0?void 0:(_toolTipGuideMetaData4=_toolTipGuideMetaData3.containers)===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.find(container=>container.type===\"button\");return buttonContainer!==null&&buttonContainer!==void 0&&buttonContainer.buttons?/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsxs(Select// The dropdown value is always the button's ID, matching MenuItem values\n,{value:((_toolTipGuideMetaData5,_toolTipGuideMetaData6,_toolTipGuideMetaData7,_buttonContainer$butt2,_buttonContainer$butt3,_buttonContainer$butt4,_buttonContainer$butt5,_buttonContainer$butt6)=>{// Primary: Use the ButtonId from metadata as the source of truth\nconst designButtonId=(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData5===void 0?void 0:(_toolTipGuideMetaData6=_toolTipGuideMetaData5.design)===null||_toolTipGuideMetaData6===void 0?void 0:(_toolTipGuideMetaData7=_toolTipGuideMetaData6.gotoNext)===null||_toolTipGuideMetaData7===void 0?void 0:_toolTipGuideMetaData7.ButtonId;if(designButtonId&&buttonContainer!==null&&buttonContainer!==void 0&&(_buttonContainer$butt2=buttonContainer.buttons)!==null&&_buttonContainer$butt2!==void 0&&_buttonContainer$butt2.some(button=>button.id===designButtonId)){return designButtonId;}// Secondary: Use btnidss if it matches a valid button\nif(btnidss&&buttonContainer!==null&&buttonContainer!==void 0&&(_buttonContainer$butt3=buttonContainer.buttons)!==null&&_buttonContainer$butt3!==void 0&&_buttonContainer$butt3.some(button=>button.id===btnidss)){return btnidss;}// Tertiary: Use dropdownValue if it matches a valid button\nif(dropdownValue&&buttonContainer!==null&&buttonContainer!==void 0&&(_buttonContainer$butt4=buttonContainer.buttons)!==null&&_buttonContainer$butt4!==void 0&&_buttonContainer$butt4.some(button=>button.id===dropdownValue)){return dropdownValue;}// Fallback: if elementButtonName matches a button name, use its id\nif(elementButtonName&&buttonContainer!==null&&buttonContainer!==void 0&&(_buttonContainer$butt5=buttonContainer.buttons)!==null&&_buttonContainer$butt5!==void 0&&_buttonContainer$butt5.length){const match=buttonContainer.buttons.find(button=>button.name===elementButtonName);if(match)return match.id;}// Fallback: if ButtonId is present but ButtonName is missing, use ButtonId and display the name by lookup\nif(designButtonId&&buttonContainer!==null&&buttonContainer!==void 0&&(_buttonContainer$butt6=buttonContainer.buttons)!==null&&_buttonContainer$butt6!==void 0&&_buttonContainer$butt6.length){const match=buttonContainer.buttons.find(button=>button.id===designButtonId);if(match)return designButtonId;}// Default to empty string\nreturn\"\";})(),onChange:handleDropdownChange,displayEmpty:true,style:{width:\"100%\"},sx:{\"& .MuiSvgIcon-root\":{height:\"20px\",width:\"20px\",top:\"10px\"}},renderValue:selected=>{var _buttonContainer$butt7;// Always display the button name for the selected ID\nif(!selected)return translate(\"Select an option\");const btn=buttonContainer===null||buttonContainer===void 0?void 0:(_buttonContainer$butt7=buttonContainer.buttons)===null||_buttonContainer$butt7===void 0?void 0:_buttonContainer$butt7.find(button=>button.id===selected);return(btn===null||btn===void 0?void 0:btn.name)||translate(\"Select an option\");},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"\",disabled:true,children:translate(\"Select an option\")}),buttonContainer.buttons.map((button,buttonIndex)=>/*#__PURE__*/_jsx(MenuItem,{value:button.id,children:button.name},buttonIndex))]})}):null;})(),\" \"]})})]}),selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"?/*#__PURE__*/_jsxs(Button,{className:\"qadpt-design-btn\",onClick:handleHotspotClick// Trigger the hotspot popup\n,children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-hotsicon\",dangerouslySetInnerHTML:{__html:Hotspoticon}}),/*#__PURE__*/_jsx(Typography,{sx:{fontWeight:\"600 !important\"},children:translate(\"Hotspot\")})]}):\"\",selectedTemplate===\"Checklist\"&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:handleCheckPointPopup,startIcon:/*#__PURE__*/_jsx(DesignServicesIcon,{}),children:translate(\"Steps\")}),/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:handleTitlePopup,startIcon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:elements},style:{height:\"23px\"}}),children:translate(\"Title & SubTitle\")})]}),checkpointsPopup&&/*#__PURE__*/_jsx(_Fragment,{children:/*#__PURE__*/_jsx(Checkpoints,{})}),/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:toggleCanvasSettings,startIcon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:designicon},style:{height:\"23px\"}}),children:translate(\"Canvas\")}),selectedTemplate!=\"Checklist\"&&/*#__PURE__*/_jsxs(\"span\",{children:[/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:toggleElementsSettings,startIcon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:elements},style:{height:\"23px\"}}),children:translate(\"Elements\")}),([\"Tooltip\",\"Announcement\"].includes(selectedTemplate)||selectedTemplate===\"Tour\"&&[\"Tooltip\",\"Announcement\"].includes(selectedTemplateTour))&&/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:toggleOverlaySettings,startIcon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:overlay},style:{height:\"23px\"}})// sx={{\n// \topacity: selectedTemplate === \"Banner\" ? 0.5 : 1,\n// }}\n,children:translate(\"Overlay\")})]}),selectedTemplate===\"Checklist\"&&/*#__PURE__*/_jsx(Button,{className:\"qadpt-design-btn\",onClick:handleLaunchSettings,startIcon:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:animation},style:{height:\"23px\"}}),children:translate(\"Launcher\")})]})})]}),hotspotPopup&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(HotspotSettings,{})}),showCanvasSettings&&(selectedTemplate===\"Banner\"||selectedTemplateTour===\"Banner\")?/*#__PURE__*/_jsx(PageInteractions,{setShowCanvasSettings:setShowCanvasSettings,backgroundC:backgroundC,setBackgroundC:setBackgroundC,Bposition:Bposition,setBposition:setBposition,bpadding:bpadding,setbPadding:setbPadding,Bbordercolor:Bbordercolor,setBBorderColor:setBBorderColor,BborderSize:BborderSize,setBBorderSize:setBBorderSize,zindeex:zindeex,setZindeex:setZindeex,resetHeightofBanner:resetHeightofBanner}):showCanvasSettings&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(CanvasSettings,{zindeex:zindeex,setZindeex:setZindeex,setShowCanvasSettings:setShowCanvasSettings// width={width}\n// height={height}\n// padding={padding}\n// borderRadius={borderRadius}\n// borderColor={borderColor}\n// backgroundColor={backgroundColor}\n// selectedPosition={selectedPosition}\n// setSelectedPosition={setSelectedPosition}\n// setBorderColor={setBorderColor}\n// setBackgroundColor={setBackgroundColor}\n// setWidth={setWidth}\n// setHeight={setHeight}\n// setPadding={setPadding}\n// setBorderRadius={setBorderRadius}\n})}),showTooltipCanvasSettings&&(selectedTemplate===\"Tooltip\"||selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Hotspot\")?/*#__PURE__*/_jsx(TooltipCanvasSettings,{setShowTooltipCanvasSettings:setShowTooltipCanvasSettings,backgroundC:backgroundC,setBackgroundC:setBackgroundC,Bposition:Bposition,setBposition:setBposition,bpadding:bpadding,setbPadding:setbPadding,Bbordercolor:Bbordercolor,setBBorderColor:setBBorderColor,BborderSize:BborderSize,setBBorderSize:setBBorderSize,zindeex:zindeex,setZindeex:setZindeex}):\"\",showChecklistCanvasSettings&&selectedTemplate===\"Checklist\"?/*#__PURE__*/_jsx(ChecklistCanvasSettings,{setShowChecklistCanvasSettings:setShowChecklistCanvasSettings,backgroundC:backgroundC,setBackgroundC:setBackgroundC,Bposition:Bposition,setBposition:setBposition,bpadding:bpadding,setbPadding:setbPadding,Bbordercolor:Bbordercolor,setBBorderColor:setBBorderColor,BborderSize:BborderSize,setBBorderSize:setBBorderSize,zindeex:zindeex,setZindeex:setZindeex}):\"\",showOverlay&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(OverlaySettings,{setOverlaySettings:setOverlaySettings,selectedTemplate:selectedTemplate,onStatusChange:handleStatusChange,setOverLays:setOverLays,setDesignPopup:setDesignPopup,anchorEl:anchorEl,setMenuPopup:setMenuPopup})}),showElementsSettings&&designPopup&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Elementssettings,{setShowElementsSettings:setShowElementsSettings,setDesignPopup:setDesignPopup,setMenuPopup:setMenuPopup,resetHeightofBanner:resetHeightofBanner})}),showCustomCSS&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(CustomCSS,{})}),showAnimation&&/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(AnimationSettings,{selectedTemplate:selectedTemplate})})]})//</Draggable>\n);};export default DesignMenu;function setTooltipXaxis(arg0){throw new Error(\"Function not implemented.\");}function setTooltipYaxis(arg0){throw new Error(\"Function not implemented.\");}function setTooltipPosition(arg0){throw new Error(\"Function not implemented.\");}function setTooltipBorderradius(arg0){throw new Error(\"Function not implemented.\");}function setTooltipPadding(arg0){throw new Error(\"Function not implemented.\");}function setTooltipWidth(arg0){throw new Error(\"Function not implemented.\");}function updateCanvasInTooltip(CANVAS_DEFAULT_VALUE){throw new Error(\"Function not implemented.\");}function setElementSelected(arg0){throw new Error(\"Function not implemented.\");}function setIsTooltipPopup(arg0){throw new Error(\"Function not implemented.\");}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "Box", "Typography", "IconButton", "Select", "MenuItem", "CloseIcon", "DesignServicesIcon", "CanvasSettings", "Elementssettings", "OverlaySettings", "CustomCSS", "PageInteractions", "AnimationSettings", "useDrawerStore", "HotspotSettings", "animation", "elements", "Hotspoticon", "overlay", "Reselect", "designicon", "TooltipCanvasSettings", "KeyboardTabSharp", "GetGudeDetailsByGuideId", "userSession", "ChecklistCanvasSettings", "LauncherSettings", "Checkpoints", "TitleSubTitle", "useTranslation", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "DesignMenu", "_ref", "width", "height", "overlays", "setOverLays", "backgroundC", "setBackgroundC", "Bposition", "setBposition", "bpadding", "setbPadding", "Bbordercolor", "setBBorderColor", "BborderSize", "setBBorderSize", "zindeex", "setZindeex", "setDesignPopup", "selectedTemplate", "designPopup", "initialGuideData", "updatedGuideData", "handleSaveGuide", "resetHeightofBanner", "t", "translate", "showCanvasSettings", "setShowCanvasSettings", "showChecklistCanvasSettings", "setShowChecklistCanvasSettings", "showOverlay", "setOverlaySettings", "showElementsSettings", "setShowElementsSettings", "showAnimation", "setshowAnimation", "showCustomCSS", "setShowCustomCSS", "anchorEl", "setAnchorEl", "reselectElement", "setReselectElement", "goToNextElement", "setGoToNextElement", "isOpen", "setIsOpen", "setCurrentGuideId", "currentGuideId", "getCurrentGuideId", "state", "padding", "setPadding", "position", "setPosition", "radius", "setRadius", "borderSize", "setBorderSize", "setBorderColor", "borderColor", "setBackgroundColor", "backgroundColor", "overlayEnabled", "setOverlayEnabled", "setZiindex", "setguidesSettingspopup", "setHotspotPopup", "setTitlePopup", "titlePopup", "hotspotPopup", "showTooltipCanvasSettings", "setShowTooltipCanvasSettings", "setTooltipBackgroundcolor", "setTooltipBordercolor", "setTooltipBorderradius", "setTooltipBordersize", "CANVAS_DEFAULT_VALUE", "savedGuideData", "ButtonsDropdown", "setButtonsDropdown", "elementSelected", "setElementSelected", "currentHoveredElement", "elementClick", "setElementClick", "elementButtonName", "setElementButtonName", "updateDesignelementInTooltip", "toolTipGuideMetaData", "elementbuttonClick", "SetElementButtonClick", "buttonClick", "setButtonClick", "currentStep", "highlighted<PERSON><PERSON><PERSON>", "setHighlightedButton", "setSelectActions", "updateTooltipButtonAction", "mapButtonSection", "btnidss", "selectedTemplateTour", "progress", "setProgress", "setSelectedOption", "dropdownValue", "setDropdownValue", "setIsUnSavedChanges", "showLauncherSettings", "setShowLauncherSettings", "checkpointsPopup", "setCheckPointsPopup", "createWithAI", "interactionData", "setbtnidss", "toggleCanvasSettings", "toggleOverlaySettings", "setMenuPopup", "handleHotspotClick", "setTimeout", "handleTitlePopup", "handleCheckPointPopup", "toggleReselectElement", "setIsTooltipPopup", "fetchGuideDetails", "_tooltipMetadata$desi", "tooltipMetadata", "design", "gotoNext", "hasButtonClick", "ButtonId", "trim", "console", "log", "ButtonName", "buttonName", "_data$GuideDetails", "_data$GuideDetails$Gu", "_guideStep$Design", "data", "guideStep", "GuideDetails", "GuideStep", "Design", "GotoNext", "removeAppliedStyleOfEle", "element", "removeAttribute", "style", "outline", "pointerEvents", "menuPopup", "onReselectElement", "existingHotspot", "document", "getElementById", "existingTooltip", "handleButtonClick", "buttonId", "handleToggleClick", "prev", "handleLaunchSettings", "type", "updatedCanvasSettings", "NextStep", "<PERSON>ement<PERSON><PERSON>", "Id", "_tooltipMetadata$desi2", "_tooltipMetadata$desi3", "DesignelementInTooltip", "value", "name", "handleDropdownChange", "event", "_toolTipGuideMetaData", "_toolTipGuideMetaData2", "_buttonContainer$butt", "selected<PERSON><PERSON><PERSON>", "target", "buttonContainer", "containers", "find", "container", "<PERSON><PERSON><PERSON><PERSON>", "buttons", "button", "id", "toggleElementsSettings", "children", "toggleCustomCSS", "toggleAnimation", "handleClose", "handleDismissDataChange", "handleStatusChange", "status", "className", "size", "onClick", "sx", "cursor", "color", "dangerouslySetInnerHTML", "__html", "marginRight", "marginBottom", "borderRadius", "background", "textTransform", "display", "justifyContent", "alignItems", "fontWeight", "gap", "marginTop", "border", "fontSize", "margin", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_buttonContainer$butt2", "_buttonContainer$butt3", "_buttonContainer$butt4", "_buttonContainer$butt5", "_buttonContainer$butt6", "designButtonId", "some", "length", "match", "onChange", "displayEmpty", "top", "renderValue", "selected", "_buttonContainer$butt7", "btn", "disabled", "map", "buttonIndex", "startIcon", "includes", "onStatusChange", "setTooltipXaxis", "arg0", "Error", "setTooltipYaxis", "setTooltipPosition", "setTooltipPadding", "setTooltipWidth", "updateCanvasInTooltip"], "sources": ["E:/Code/Quickadopt Bugs Devlatest/quickadapt/QuickAdaptExtension/src/components/guideDesign/Design.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\n// import Draggable from \"react-draggable\";\r\nimport { Button, Box, Typography, IconButton, Select, MenuItem, Step } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport DesignServicesIcon from \"@mui/icons-material/DesignServices\";\r\nimport ViewModuleIcon from \"@mui/icons-material/ViewModule\";\r\nimport CodeIcon from \"@mui/icons-material/Code\";\r\nimport CanvasSettings from \"./CanvasSettings\";\r\nimport Elementssettings from \"../guideSetting/ElementsSettings\";\r\nimport OverlaySettings from \"./Overlay\";\r\nimport CustomCSS from \"./CustomCss\";\r\nimport PageInteractions from \"../guideBanners/selectedpopupfields/PageInteraction\";\r\nimport AnimationSettings from \"./Animation\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport Tooltip from \"@mui/material/Tooltip\";\r\nimport HotspotSettings from \"../hotspot/HotspotSettings\";\r\nimport { animation, elements, Hotspoticon, overlay, Reselect, designicon } from \"../../assets/icons/icons\";\r\nimport TooltipCanvasSettings from \"../Tooltips/designFields/TooltipCanvasSettings\";\r\nimport { TouchAppSharp, KeyboardTabSharp } from \"@mui/icons-material\";\r\nimport { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport userSession from \"../../store/userSession\";\r\nimport ChecklistCanvasSettings from \"../checklist/ChecklistCanvasSettings\";\r\nimport LauncherSettings from \"../checklist/LauncherSettings\";\r\nimport Checkpoints from \"../checklist/Chekpoints\";\r\nimport TitleSubTitle from \"../checklist/TitleSubTitle\";\r\nimport '../../styles/rtl_styles.scss';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst DesignMenu = ({\r\n\twidth,\r\n\theight,\r\n\toverlays,\r\n\tsetOverLays,\r\n\tbackgroundC,\r\n\tsetBackgroundC,\r\n\tBposition,\r\n\tsetBposition,\r\n\tbpadding,\r\n\tsetbPadding,\r\n\tBbordercolor,\r\n\tsetBBorderColor,\r\n\tBborderSize,\r\n\tsetBBorderSize,\r\n\tzindeex,\r\n\tsetZindeex,\r\n\tsetDesignPopup,\r\n\tselectedTemplate,\r\n\tdesignPopup,\r\n\tinitialGuideData,\r\n\tupdatedGuideData,\r\n\thandleSaveGuide,\r\n\tresetHeightofBanner,\r\n}: // hotspotPopup\r\n// padding,\r\n// borderRadius,\r\n// borderColor,\r\n// backgroundColor,\r\n// selectedPosition,\r\n// setSelectedPosition,\r\n// setBorderColor,\r\n// setBackgroundColor,\r\n// setWidth,\r\n// setHeight,\r\n// setPadding,\r\n// setBorderRadius,\r\n// //selectedTemplate,\r\n// position,\r\n// setPosition,\r\n// radius,\r\n// setRadius,\r\n// borderSize,\r\n// setBorderSize,\r\nany) => {\r\n\tconst { t: translate } = useTranslation();\r\n\t// State to control the visibility of CanvasSettings\r\n\tconst [showCanvasSettings, setShowCanvasSettings] = useState(false);\r\n\tconst [showChecklistCanvasSettings, setShowChecklistCanvasSettings] = useState(false);\r\n\t//const [showTooltipCanvasSettings, setShowTooltipCanvasSettings] = useState(false);\r\n\tconst [showOverlay, setOverlaySettings] = useState(false);\r\n\tconst [showElementsSettings, setShowElementsSettings] = useState(false);\r\n\tconst [showAnimation, setshowAnimation] = useState(false);\r\n\tconst [showCustomCSS, setShowCustomCSS] = useState(false);\r\n\tconst [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);\r\n\tconst [reselectElement, setReselectElement] = useState(false);\r\n\tconst [goToNextElement, setGoToNextElement] = useState(false);\r\n\t// const [elementClick, setElementClick] = useState(false);\r\n\t//const [dropdownValue, setDropdownValue] = useState(\"\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst { setCurrentGuideId, currentGuideId, getCurrentGuideId } = userSession((state: any) => state);\r\n\tconst {\r\n\t\t//selectedTemplate,\r\n\t\tpadding,\r\n\t\tsetPadding,\r\n\t\tposition,\r\n\t\tsetPosition,\r\n\t\tradius,\r\n\t\tsetRadius,\r\n\t\tborderSize,\r\n\t\tsetBorderSize,\r\n\t\tsetBorderColor,\r\n\t\tborderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tbackgroundColor,\r\n\t\toverlayEnabled,\r\n\t\tsetOverlayEnabled,\r\n\t\tsetZiindex,\r\n\t\tsetguidesSettingspopup,\r\n\t\tsetHotspotPopup,\r\n\t\tsetTitlePopup,\r\n\t\ttitlePopup,\r\n\t\thotspotPopup,\r\n\t\tshowTooltipCanvasSettings,\r\n\t\tsetShowTooltipCanvasSettings,\r\n\t\tsetTooltipBackgroundcolor,\r\n\t\tsetTooltipBordercolor,\r\n\t\tsetTooltipBorderradius,\r\n\t\tsetTooltipBordersize,\r\n\t\tCANVAS_DEFAULT_VALUE,\r\n\t\tsavedGuideData,\r\n\t\tButtonsDropdown,\r\n\t\tsetButtonsDropdown,\r\n\t\telementSelected,\r\n\t\tsetElementSelected,\r\n\t\tcurrentHoveredElement,\r\n\t\telementClick,\r\n\t\tsetElementClick,\r\n\t\telementButtonName,\r\n\t\tsetElementButtonName,\r\n\t\tupdateDesignelementInTooltip,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementbuttonClick,\r\n\t\tSetElementButtonClick,\r\n\t\tbuttonClick,\r\n\t\tsetButtonClick,\r\n\t\tcurrentStep,\r\n\t\thighlightedButton,\r\n\t\tsetHighlightedButton,\r\n\t\tsetSelectActions,\r\n\t\tupdateTooltipButtonAction,\r\n\t\tmapButtonSection,\r\n\t\tbtnidss,\r\n\t\tselectedTemplateTour,\r\n\t\tprogress,\r\n\t\tsetProgress,\r\n\t\tsetSelectedOption,\r\n\t\tdropdownValue,\r\n\t\tsetDropdownValue,\r\nsetIsUnSavedChanges,\r\n\t\tshowLauncherSettings,\r\n\t\tsetShowLauncherSettings,\r\n\t\tcheckpointsPopup,\r\n\t\tsetCheckPointsPopup,\r\n\t\tcreateWithAI,\r\n\t\tinteractionData\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst setbtnidss = useDrawerStore((state) => state.setbtnidss);\r\n\r\n\tuseEffect(() => {\r\n\t\tsetShowCanvasSettings(false);\r\n\t\tsetShowChecklistCanvasSettings(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t\tsetOverlaySettings(false);\r\n\t\tsetShowElementsSettings(false);\r\n\t\tsetshowAnimation(false);\r\n\t\tsetShowCustomCSS(false);\r\n\t\tsetHotspotPopup(false);\r\n\t\tsetTitlePopup(false);\r\n\t\tsetShowLauncherSettings(false);\r\n\t}, [selectedTemplate, selectedTemplateTour]);\r\n\t// const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);\r\n\t// const setOverlayEnabled = useDrawerStore((state) => state.setOverlayEnabled);\r\n\tconst toggleCanvasSettings = () => {\r\n\t\tif (\r\n\t\t\tselectedTemplate === \"Tooltip\" ||\r\n\t\t\tselectedTemplate === \"Hotspot\" ||\r\n\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t) {\r\n\t\t\tsetShowTooltipCanvasSettings(!showTooltipCanvasSettings);\r\n\t\t}\r\n\t\telse if (selectedTemplate === \"Checklist\")\r\n\t\t{\r\n\t\t\tsetShowChecklistCanvasSettings(!showChecklistCanvasSettings);\r\n\t\t}\r\n\t\telse {\r\n\t\t\tsetShowCanvasSettings(!showCanvasSettings);\r\n\t\t}\r\n\t};\r\n\tconst toggleOverlaySettings = () => {\r\n\t\tsetMenuPopup(false);\r\n\t\tsetOverlaySettings(!showOverlay);\r\n\t};\r\n\tconst handleHotspotClick = () => {\r\n\t\t//setguidesSettingspopup(false); // Close any other popups\r\n\t\tsetHotspotPopup(true); // Open the hotspot popup\r\n\t\tsetTimeout(() => {\r\n\t\t\tsetHotspotPopup(true); // Ensure the popup is rendered\r\n\t\t}, 0);\r\n\t};\r\n\r\n\tconst handleTitlePopup = () =>\r\n\t{\r\n\t\tsetTitlePopup(true);\r\n\t}\r\n\tconst handleCheckPointPopup = () =>\r\n\t{\r\n\t\tsetCheckPointsPopup(true);\r\n\t}\r\n\r\n\t// useEffect(() => {\r\n\t// \tsetTimeout(() => {\r\n\t// \t\tsetHotspotPopup(true); // Ensure the popup is rendered\r\n\t// \t}, 0);\r\n\t// }, [hotspotPopup]);\r\n\r\n\t// Removed useEffect that was resetting dropdownValue on currentStep change\r\n\t// This was causing the dropdown to show \"Select an option\" even after selection\r\n\r\n\r\n\tconst toggleReselectElement = () => {\r\n\t\t//setReselectElement(!reselectElement);\r\n\t\t//setTooltipXaxis(\"4\");\r\n\t\t//setTooltipYaxis(\"4\");\r\n\t\t//setTooltipPosition(\"middle-center\");\r\n\t\t//setTooltipBackgroundcolor(\"\");\r\n\t\t//setTooltipBordercolor(\"\");\r\n\t\tsetTooltipBorderradius(\"8\");\r\n\t\t//setTooltipBordersize(\"1\");\r\n\t\t//setTooltipPadding(\"4\");\r\n\t\t//setTooltipWidth(\"400\");\r\n\t\t//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\tsetElementSelected(true);\r\n\t\tsetIsTooltipPopup(false);\r\n\t\tsetShowTooltipCanvasSettings(false);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t  if (currentGuideId != \"\" && currentGuideId != null) {\r\n\t\t\t// First, check the current toolTipGuideMetaData for the most up-to-date state\r\n\t\t\tconst tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];\r\n\r\n\t\t\tif (tooltipMetadata?.design?.gotoNext) {\r\n\t\t\t  // Use the current metadata as the source of truth\r\n\t\t\t  const hasButtonClick = tooltipMetadata.design.gotoNext.ButtonId &&\r\n\t\t\t\ttooltipMetadata.design.gotoNext.ButtonId.trim() !== \"\";\r\n\r\n\t\t\t  console.log(\"useEffect: Has button click:\", hasButtonClick, \"ButtonId:\", tooltipMetadata.design.gotoNext.ButtonId);\r\n\r\n\t\t\t  if (hasButtonClick) {\r\n\t\t\t\tsetElementClick(\"button\");\r\n\t\t\t\tsetButtonClick(true);\r\n\t\t\t\tSetElementButtonClick(true);\r\n\t\t\t\t// Use ButtonId for dropdown value, not ButtonName\r\n\t\t\t\tsetDropdownValue(tooltipMetadata.design.gotoNext.ButtonId || \"\");\r\n\t\t\t\tsetElementButtonName(tooltipMetadata.design.gotoNext.ButtonName || tooltipMetadata.design.gotoNext.buttonName || \"\");\r\n\t\t\t\tsetbtnidss(tooltipMetadata.design.gotoNext.ButtonId || \"\");\r\n\r\n\t\t\t\tconsole.log(\"useEffect: Set button click mode with values:\", {\r\n\t\t\t\t\telementClick: \"button\",\r\n\t\t\t\t\tbuttonClick: true,\r\n\t\t\t\t\tdropdownValue: tooltipMetadata.design.gotoNext.ButtonId,\r\n\t\t\t\t\telementButtonName: tooltipMetadata.design.gotoNext.ButtonName,\r\n\t\t\t\t\tbtnidss: tooltipMetadata.design.gotoNext.ButtonId\r\n\t\t\t\t});\r\n\t\t\t  } else {\r\n\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\tsetButtonClick(false);\r\n\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\tsetDropdownValue(\"\");\r\n\t\t\t\tsetElementButtonName(\"\");\r\n\t\t\t\tsetbtnidss(\"\");\r\n\r\n\t\t\t\tconsole.log(\"useEffect: Set element click mode\");\r\n\t\t\t  }\r\n\t\t\t} else {\r\n\t\t\t  // Fallback to fetching from database if metadata doesn't exist\r\n\t\t\t  const data = await GetGudeDetailsByGuideId(currentGuideId, createWithAI, interactionData);\r\n\t\t\t  const guideStep = data?.GuideDetails?.GuideStep?.[currentStep - 1];\r\n\r\n\t\t\t  if (guideStep?.Design?.GotoNext) {\r\n\t\t\t\tconst hasButtonClick = guideStep.Design.GotoNext.ButtonId &&\r\n\t\t\t\t  guideStep.Design.GotoNext.ButtonId.trim() !== \"\";\r\n\r\n\t\t\t\tif (hasButtonClick) {\r\n\t\t\t\t  setElementClick(\"button\");\r\n\t\t\t\t  setButtonClick(true);\r\n\t\t\t\t  SetElementButtonClick(true);\r\n\t\t\t\t  // Use ButtonId for dropdown value, not ButtonName\r\n\t\t\t\t  setDropdownValue(guideStep.Design.GotoNext.ButtonId || \"\");\r\n\t\t\t\t  setElementButtonName(guideStep.Design.GotoNext.ButtonName || \"\");\r\n\t\t\t\t  setbtnidss(guideStep.Design.GotoNext.ButtonId || \"\");\r\n\t\t\t\t} else {\r\n\t\t\t\t  setElementClick(\"element\");\r\n\t\t\t\t  setButtonClick(false);\r\n\t\t\t\t  SetElementButtonClick(false);\r\n\t\t\t\t  setDropdownValue(\"\");\r\n\t\t\t\t  setElementButtonName(\"\");\r\n\t\t\t\t  setbtnidss(\"\");\r\n\t\t\t\t}\r\n\t\t\t  } else {\r\n\t\t\t\tsetElementClick(\"element\");\r\n\t\t\t\tsetButtonClick(false);\r\n\t\t\t\tSetElementButtonClick(false);\r\n\t\t\t\tsetDropdownValue(\"\");\r\n\t\t\t\tsetElementButtonName(\"\");\r\n\t\t\t\tsetbtnidss(\"\");\r\n\t\t\t  }\r\n\t\t\t}\r\n\t\t  }\r\n\t\t};\r\n\t\tfetchGuideDetails();\r\n\t  }, [currentStep, toolTipGuideMetaData]);\r\n\r\n\r\n\tconst removeAppliedStyleOfEle = (element: HTMLElement) => {\r\n\t\telement.removeAttribute(\"disabled\");\r\n\t\telement.style.outline = \"\";\r\n\t\telement.style.pointerEvents = \"unset\";\r\n\t};\r\n\tconst [menuPopup, setMenuPopup] = useState(true);\r\n\tconst onReselectElement = () => {\r\n\t\t// setTooltipXaxis(\"4\");\r\n\t\t// setTooltipYaxis(\"4\");\r\n\t\t// setTooltipPosition(\"middle-center\");\r\n\t\t// setTooltipBackgroundcolor(\"\");\r\n\t\t// setTooltipBordercolor(\"\");\r\n\t\t// setTooltipBorderradius(\"4\");\r\n\t\t// setTooltipBordersize(\"1\");\r\n\t\t// setTooltipPadding(\"4\");\r\n\t\t// setTooltipWidth(\"400\");\r\n\t\t//updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\t//setElementSelected(true);\r\n\t\t// setElementSelected(false);\r\n\t\t// setIsTooltipPopup(false);\r\n\t\t// setShowTooltipCanvasSettings(false);\r\n\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\tconst existingTooltip = document.getElementById(\"Tooltip-unique\");\r\n\t\tsetIsUnSavedChanges(false);\r\n\r\n\t\t\t// existingTooltip.remove();\r\n\t\t\tsetDesignPopup(false);\r\n\t\t\tsetElementSelected(false);\r\n\t\t\tcurrentHoveredElement && removeAppliedStyleOfEle(currentHoveredElement);\r\n\r\n\t\tsetIsUnSavedChanges(true);\r\n\t};\r\n\r\n\t// const [highlightedButton, setHighlightedButton] = useState(null);\r\n\r\n\t// Function to handle button highlighting\r\n\tconst handleButtonClick = (buttonId: any) => {\r\n\t\tconsole.log(\"Button clicked:\", buttonId);\r\n\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\thandleToggleClick(buttonId);\r\n\t\tsetHighlightedButton((prev: any) => (prev === buttonId ? null : buttonId));\r\n\r\n\t\t// Remove immediate save to prevent state interference\r\n\t\t// handleSaveGuide();\r\n\t};\r\n\r\n\tconst handleLaunchSettings = () =>\r\n\t{\r\n\t\tsetShowLauncherSettings(true);\r\n\t\t}\r\n\tconst handleToggleClick = (type: any) => {\r\n\t\tconsole.log(\"handleToggleClick called with type:\", type);\r\n\r\n\t\tif (type === 1) {\r\n\t\t\t// Switching to \"element click\"\r\n\t\t\tconsole.log(\"Switching to element click mode\");\r\n\t\t\tsetElementClick(\"element\");\r\n\t\t\tsetElementButtonName(\"\");\r\n\t\t\tsetDropdownValue(\"\");\r\n\t\t\tsetbtnidss(\"\");\r\n\t\t\tsetButtonClick(false);\r\n\t\t\tSetElementButtonClick(false);\r\n\r\n\t\t\t// Update the gotoNext object to reflect \"element click\" state\r\n\t\t\tconst updatedCanvasSettings = {\r\n\t\t\t\tNextStep: \"element\",\r\n\t\t\t\tButtonId: \"\",\r\n\t\t\t\tElementPath: \"\",\r\n\t\t\t\tButtonName: \"\",\r\n\t\t\t\tId: \"\"\r\n\t\t\t};\r\n\t\t\tupdateDesignelementInTooltip(updatedCanvasSettings);\r\n\r\n\t\t} else if (type === 2) {\r\n\t\t\t// Switching to \"button click\"\r\n\t\t\tconsole.log(\"Switching to button click mode\");\r\n\t\t\tsetElementClick(\"button\");\r\n\t\t\tsetButtonClick(true);\r\n\t\t\tSetElementButtonClick(true);\r\n\r\n\t\t\t// CRITICAL FIX: When switching to button click, restore the dropdown state from metadata\r\n\t\t\tconst tooltipMetadata = toolTipGuideMetaData?.[currentStep - 1];\r\n\t\t\tconsole.log(\"Current tooltip metadata:\", tooltipMetadata);\r\n\r\n\t\t\tif (tooltipMetadata?.design?.gotoNext?.ButtonId) {\r\n\t\t\t\tconst buttonId = tooltipMetadata.design.gotoNext.ButtonId;\r\n\t\t\t\tconst buttonName = tooltipMetadata.design.gotoNext.ButtonName;\r\n\r\n\t\t\t\t// Update all related state variables to ensure dropdown shows correctly\r\n\t\t\t\tsetDropdownValue(buttonId);\r\n\t\t\t\tsetElementButtonName(buttonName || \"\");\r\n\t\t\t\tsetbtnidss(buttonId);\r\n\r\n\t\t\t\tconsole.log(\"Restored button click state when switching to button mode:\", {\r\n\t\t\t\t\tbuttonId,\r\n\t\t\t\t\tbuttonName,\r\n\t\t\t\t\tdropdownValue: buttonId\r\n\t\t\t\t});\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log(\"No existing button click data found in metadata\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\tconst DesignelementInTooltip = (value: any, name: any) => {\r\n\t\tconst updatedCanvasSettings = {\r\n\t\t\tNextStep: elementClick,\r\n\t\t\tButtonId: value,\r\n\t\t\tElementPath: \"\",\r\n\t\t\tButtonName: name?.name,\r\n\t\t\tId: value, // Add the Id property to match what's expected in Tooltips.tsx\r\n\t\t};\r\n\r\n\t\tupdateDesignelementInTooltip(updatedCanvasSettings);\r\n\t};\r\n\r\n\tconst handleDropdownChange = (event: any) => {\r\n\t\tconst selectedValue = event.target.value;\r\n\t\tconsole.log(\"Dropdown changed to:\", selectedValue);\r\n\r\n\t\t// Find the button container dynamically instead of using hardcoded index\r\n\t\tconst buttonContainer = toolTipGuideMetaData[currentStep - 1]?.containers?.find(\r\n\t\t\t(container: any) => container.type === \"button\"\r\n\t\t);\r\n\t\tconst selectedButton = buttonContainer?.buttons?.find(\r\n\t\t\t(button: any) => button.id === selectedValue\r\n\t\t);\r\n\r\n\t\tconsole.log(\"Selected button:\", selectedButton);\r\n\t\tconsole.log(\"Button container:\", buttonContainer);\r\n\r\n\t\t// Update all relevant state variables\r\n\t\tsetDropdownValue(selectedValue);\r\n\t\tsetElementButtonName(selectedButton?.name || selectedValue); // Use button name, fallback to ID\r\n\t\tsetbtnidss(selectedValue);\r\n\t\tsetElementClick(\"button\");\r\n\r\n\t\t// Update the design metadata with both ID and name for proper persistence\r\n\t\tDesignelementInTooltip(selectedValue, selectedButton);\r\n\r\n\t\t// Mark as unsaved changes\r\n\t\tsetIsUnSavedChanges(true);\r\n\r\n\t\tconsole.log(\"Updated state after dropdown change:\", {\r\n\t\t\tdropdownValue: selectedValue,\r\n\t\t\telementButtonName: selectedButton?.name || selectedValue,\r\n\t\t\tbtnidss: selectedValue,\r\n\t\t\telementClick: \"button\"\r\n\t\t});\r\n\t};\r\n\r\n\tconst toggleElementsSettings = () => {\r\n\t\tsetMenuPopup(false);\r\n\t\tsetShowElementsSettings(true);\r\n\t\t//setDesignPopup(false);\r\n\t\treturn (\r\n\t\t\t<>\r\n\t\t\t\t{showElementsSettings && designPopup && (\r\n\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t<Elementssettings\r\n\t\t\t\t\t\t\tsetShowElementsSettings={setShowElementsSettings}\r\n\t\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t)}\r\n\t\t\t\t;\r\n\t\t\t</>\r\n\t\t);\r\n\t};\r\n\r\n\tconst toggleCustomCSS = () => {\r\n\t\tsetShowCustomCSS(!showCustomCSS); // Toggle CustomCSS visibility\r\n\t};\r\n\tconst toggleAnimation = () => {\r\n\t\tsetshowAnimation(!showAnimation); // Toggle CustomCSS visibility\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t\tsetDesignPopup(false);\r\n\t};\r\n\r\n\tconst handleDismissDataChange = (data: any) => {};\r\n\tif (!isOpen) return null;\r\n\tconst handleStatusChange = (status: boolean) => {\r\n\t\tsetOverlayEnabled(status);\r\n\t};\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Design\")}</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon />\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\r\n\t\t\t\t{titlePopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<TitleSubTitle/>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\r\n\t\t\t\t{showLauncherSettings && (\r\n\t\t\t\t\t<LauncherSettings/>\r\n\r\n\t\t\t\t)}\r\n\t\t\t\t{menuPopup && (\r\n\t\t\t\t\t<>\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tclassName=\" qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\tsx={{cursor:\"pointer\"}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ color: \"#495e58\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Reselect Element\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-reselect-icon\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Reselect }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ padding: \"5px\", marginRight: \"10px\" }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Tooltip\" || selectedTemplateTour === \"Tooltip\") && (\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t// className=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\tpadding: \"8px 12px\",\r\n\t\t\t\t\t\t\t\t\t\tbackground: \"#eae2e2\",\r\n\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{/* <Button\r\n\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\tonClick={toggleGoToNextElement}\r\n\t\t\t\t\t\tstartIcon={<KeyboardTabIcon />}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tGo to next step\r\n\t\t\t\t\t</Button> */}\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"flex-start\", alignItems: \"center\" }} className=\"qadpt-gtnext\">\r\n\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"rgba(95, 158, 160, 0.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"21px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"21px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// borderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// background: \"rgba(95, 158, 160, 0.2)\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#444444\", fontWeight: \"600\" }}>{translate(\"Go to next step\")}</Typography>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t<div style={{ display: \"flex\", gap: \"6px\", marginTop: \"10px\" }}>\r\n\t\t\t\t\t\t\t\t\t\t{/* Button 1 */}\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(1)}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\telementClick === \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: elementClick === \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"95px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* <KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#1c1b1f\", padding: \"0 6px\", fontSize: \"12px !important\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Element Click\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t\t\t\t{/* Button 2 */}\r\n\t\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonClick(2)}\r\n\t\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"flex-start\",\r\n\t\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tcursor: \"pointer\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor:\r\n\t\t\t\t\t\t\t\t\t\t\t\t\telementClick !== \"element\" ? \"rgba(95, 158, 160, 0.2)\" : \"rgb(196, 193, 193, 0.3)\",\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\tborder: elementClick !== \"element\" ? \"1px solid var(--primarycolor)\" : \"1px solid transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: \"95px\",\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{/* <KeyboardTabSharp\r\n\t\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\t\tcolor: \"var(--primarycolor)\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tmarginRight: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t/> */}\r\n\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{ color: \"#1c1b1f\", padding: \"0 11px\", fontSize: \"12px !important\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Button Click\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t{buttonClick && (\r\n\t\t\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-chos-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"0 !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tmarginBottom: \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: \"#eae2e2\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Typography sx={{  padding: \"4px\", color: \"#495e58a\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Choose Button\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t// Find the button container dynamically instead of using hardcoded index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst buttonContainer = toolTipGuideMetaData?.[currentStep - 1]?.containers?.find(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t(container: any) => container.type === \"button\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\treturn buttonContainer?.buttons ? (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// The dropdown value is always the button's ID, matching MenuItem values\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tvalue={(() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Primary: Use the ButtonId from metadata as the source of truth\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  const designButtonId = toolTipGuideMetaData[currentStep - 1]?.design?.gotoNext?.ButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (designButtonId && buttonContainer?.buttons?.some((button: any) => button.id === designButtonId)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn designButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Secondary: Use btnidss if it matches a valid button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (btnidss && buttonContainer?.buttons?.some((button: any) => button.id === btnidss)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn btnidss;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Tertiary: Use dropdownValue if it matches a valid button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (dropdownValue && buttonContainer?.buttons?.some((button: any) => button.id === dropdownValue)) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn dropdownValue;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Fallback: if elementButtonName matches a button name, use its id\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (elementButtonName && buttonContainer?.buttons?.length) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst match = buttonContainer.buttons.find((button: any) => button.name === elementButtonName);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (match) return match.id;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Fallback: if ButtonId is present but ButtonName is missing, use ButtonId and display the name by lookup\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  if (designButtonId && buttonContainer?.buttons?.length) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst match = buttonContainer.buttons.find((button: any) => button.id === designButtonId);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (match) return designButtonId;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  // Default to empty string\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  return \"\";\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t})()}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tonChange={handleDropdownChange}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ width: \"100%\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  \"& .MuiSvgIcon-root\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttop: \"10px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\trenderValue={(selected) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t// Always display the button name for the selected ID\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (!selected) return translate(\"Select an option\");\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst btn = buttonContainer?.buttons?.find((button: any) => button.id === selected);\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\treturn btn?.name || translate(\"Select an option\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"\" disabled>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{translate(\"Select an option\")}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{buttonContainer.buttons.map(\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  (button: any, buttonIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t<MenuItem key={buttonIndex} value={button.id}>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  {button.name}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  )\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t{/* The selected button name is shown in the dropdown, so no need to display it separately below. */}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t</>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t) : null;\r\n\t\t\t\t\t\t\t\t\t\t\t\t})()}{\" \"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{/* Prevent rendering until data exists */}\r\n\t\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" ? (\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleHotspotClick} // Trigger the hotspot popup\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Hotspoticon }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tsx={{fontWeight: \"600 !important\"}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Hotspot\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\"\"\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t{(selectedTemplate === \"Checklist\") && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleCheckPointPopup}\r\n\r\n\t\t\t\t\t\t\t\tstartIcon={<DesignServicesIcon />}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Steps\")}\r\n\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={handleTitlePopup}\r\n\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: elements }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Title & SubTitle\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t\t{checkpointsPopup && (\r\n\t\t\t\t\t\t\t\t<>\r\n\t\t\t\t\t\t\t\t\t<Checkpoints/>\r\n\t\t\t\t\t\t\t\t</>\r\n\r\n\t\t\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t\t\t{/* Buttons with icons */}\r\n\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\tonClick={toggleCanvasSettings}\r\n\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: designicon }}\r\n\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{translate(\"Canvas\")}\r\n\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t{selectedTemplate != \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\tonClick={toggleElementsSettings}\r\n\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: elements }}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Elements\")}\r\n\t\t\t\t\t\t\t\t\t</Button>\r\n\r\n\t\t\t\t\t\t\t\t{([\"Tooltip\", \"Announcement\"].includes(selectedTemplate) ||\r\n\t\t\t\t\t\t\t\t\t(selectedTemplate === \"Tour\" && [\"Tooltip\", \"Announcement\"].includes(selectedTemplateTour))) && (\r\n\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={toggleOverlaySettings}\r\n\t\t\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: overlay }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t\t\t// \topacity: selectedTemplate === \"Banner\" ? 0.5 : 1,\r\n\t\t\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Overlay\")}\r\n\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t{/* {selectedTemplate != \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\t\t\t\ttitle={translate(\"Coming soon\")}\r\n\t\t\t\t\t\t\t\t\tPopperProps={{\r\n\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: 9999,\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\tdisabled\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\t\t\tstartIcon={\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: animation }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.5,\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Animation\")}\r\n\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t\t\t)} */}\r\n\t\t\t\t\t\t\t{selectedTemplate === \"Checklist\" && (\r\n\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-design-btn\"\r\n\t\t\t\t\t\t\t\t\tonClick={handleLaunchSettings}\r\n\t\t\t\t\t\t\t\t\tstartIcon={<span\r\n\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: animation }}\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ height: \"23px\" }}\r\n\t\t\t\t\t\t\t\t\t/>}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Launcher\")}\r\n\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</>\r\n\t\t\t\t)}\r\n\t\t\t</div>\r\n\t\t\t{hotspotPopup && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<HotspotSettings />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\r\n\t\t\t{showCanvasSettings && (selectedTemplate === \"Banner\" || selectedTemplateTour === \"Banner\") ? (\r\n\t\t\t\t<PageInteractions\r\n\t\t\t\t\tsetShowCanvasSettings={setShowCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t\tresetHeightofBanner={resetHeightofBanner}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\tshowCanvasSettings && (\r\n\t\t\t\t\t<Box>\r\n\t\t\t\t\t\t<CanvasSettings\r\n\t\t\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t\t\t\tsetShowCanvasSettings={setShowCanvasSettings}\r\n\t\t\t\t\t\t\t// width={width}\r\n\t\t\t\t\t\t\t// height={height}\r\n\t\t\t\t\t\t\t// padding={padding}\r\n\t\t\t\t\t\t\t// borderRadius={borderRadius}\r\n\t\t\t\t\t\t\t// borderColor={borderColor}\r\n\t\t\t\t\t\t\t// backgroundColor={backgroundColor}\r\n\t\t\t\t\t\t\t// selectedPosition={selectedPosition}\r\n\t\t\t\t\t\t\t// setSelectedPosition={setSelectedPosition}\r\n\t\t\t\t\t\t\t// setBorderColor={setBorderColor}\r\n\t\t\t\t\t\t\t// setBackgroundColor={setBackgroundColor}\r\n\t\t\t\t\t\t\t// setWidth={setWidth}\r\n\t\t\t\t\t\t\t// setHeight={setHeight}\r\n\t\t\t\t\t\t\t// setPadding={setPadding}\r\n\t\t\t\t\t\t\t// setBorderRadius={setBorderRadius}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t)\r\n\t\t\t)}\r\n\r\n\t\t\t{showTooltipCanvasSettings &&\r\n\t\t\t(selectedTemplate === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplate === \"Hotspot\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\") ? (\r\n\t\t\t\t<TooltipCanvasSettings\r\n\t\t\t\t\tsetShowTooltipCanvasSettings={setShowTooltipCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\r\n{showChecklistCanvasSettings &&\r\n\t\t\t(selectedTemplate === \"Checklist\"\r\n\t\t\t) ? (\r\n\t\t\t\t<ChecklistCanvasSettings\r\n\t\t\t\t\tsetShowChecklistCanvasSettings={setShowChecklistCanvasSettings}\r\n\t\t\t\t\tbackgroundC={backgroundC}\r\n\t\t\t\t\tsetBackgroundC={setBackgroundC}\r\n\t\t\t\t\tBposition={Bposition}\r\n\t\t\t\t\tsetBposition={setBposition}\r\n\t\t\t\t\tbpadding={bpadding}\r\n\t\t\t\t\tsetbPadding={setbPadding}\r\n\t\t\t\t\tBbordercolor={Bbordercolor}\r\n\t\t\t\t\tsetBBorderColor={setBBorderColor}\r\n\t\t\t\t\tBborderSize={BborderSize}\r\n\t\t\t\t\tsetBBorderSize={setBBorderSize}\r\n\t\t\t\t\tzindeex={zindeex}\r\n\t\t\t\t\tsetZindeex={setZindeex}\r\n\t\t\t\t/>\r\n\t\t\t) : (\r\n\t\t\t\t\"\"\r\n\t\t\t)}\r\n\r\n\r\n\r\n\r\n\t\t\t{showOverlay && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<OverlaySettings\r\n\t\t\t\t\t\tsetOverlaySettings={setOverlaySettings}\r\n\t\t\t\t\t\tselectedTemplate={selectedTemplate}\r\n\t\t\t\t\t\tonStatusChange={handleStatusChange}\r\n\t\t\t\t\t\tsetOverLays={setOverLays}\r\n\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\tsetMenuPopup={setMenuPopup}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showElementsSettings && designPopup && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Elementssettings\r\n\t\t\t\t\t\tsetShowElementsSettings={setShowElementsSettings}\r\n\t\t\t\t\t\tsetDesignPopup={setDesignPopup}\r\n\t\t\t\t\t\tsetMenuPopup={setMenuPopup}\r\n\t\t\t\t\t\tresetHeightofBanner={resetHeightofBanner}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showCustomCSS && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<CustomCSS />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t\t{showAnimation && (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<AnimationSettings selectedTemplate={selectedTemplate} />\r\n\t\t\t\t</Box>\r\n\t\t\t)}\r\n\t\t</div>\r\n\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default DesignMenu;\r\nfunction setTooltipXaxis(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipYaxis(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipPosition(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipBorderradius(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipPadding(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setTooltipWidth(arg0: string) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction updateCanvasInTooltip(CANVAS_DEFAULT_VALUE: any) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setElementSelected(arg0: boolean) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n\r\nfunction setIsTooltipPopup(arg0: boolean) {\r\n\tthrow new Error(\"Function not implemented.\");\r\n}\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,KAAQ,OAAO,CAClD;AACA,OAASC,MAAM,CAAEC,GAAG,CAAEC,UAAU,CAAEC,UAAU,CAAEC,MAAM,CAAEC,QAAQ,KAAc,eAAe,CAC3F,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,kBAAkB,KAAM,oCAAoC,CAGnE,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,MAAO,CAAAC,eAAe,KAAM,WAAW,CACvC,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,gBAAgB,KAAM,qDAAqD,CAClF,MAAO,CAAAC,iBAAiB,KAAM,aAAa,CAC3C,MAAO,qBAAqB,CAC5B,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CAEpD,MAAO,CAAAC,eAAe,KAAM,4BAA4B,CACxD,OAASC,SAAS,CAAEC,QAAQ,CAAEC,WAAW,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,UAAU,KAAQ,0BAA0B,CAC1G,MAAO,CAAAC,qBAAqB,KAAM,gDAAgD,CAClF,OAAwBC,gBAAgB,KAAQ,qBAAqB,CACrE,OAASC,uBAAuB,KAAQ,kCAAkC,CAC1E,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,uBAAuB,KAAM,sCAAsC,CAC1E,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,8BAA8B,CACrC,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,UAAU,CAAGC,IAAA,EA4CX,IA5CY,CACnBC,KAAK,CACLC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,WAAW,CACXC,cAAc,CACdC,SAAS,CACTC,YAAY,CACZC,QAAQ,CACRC,WAAW,CACXC,YAAY,CACZC,eAAe,CACfC,WAAW,CACXC,cAAc,CACdC,OAAO,CACPC,UAAU,CACVC,cAAc,CACdC,gBAAgB,CAChBC,WAAW,CACXC,gBAAgB,CAChBC,gBAAgB,CAChBC,eAAe,CACfC,mBAqBC,CAAC,CAAAvB,IAAA,CACF,KAAM,CAAEwB,CAAC,CAAEC,SAAU,CAAC,CAAGjC,cAAc,CAAC,CAAC,CACzC;AACA,KAAM,CAACkC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlE,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAACmE,2BAA2B,CAAEC,8BAA8B,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CACrF;AACA,KAAM,CAACqE,WAAW,CAAEC,kBAAkB,CAAC,CAAGtE,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACuE,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGxE,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACyE,aAAa,CAAEC,gBAAgB,CAAC,CAAG1E,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC2E,aAAa,CAAEC,gBAAgB,CAAC,CAAG5E,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAAC6E,QAAQ,CAAEC,WAAW,CAAC,CAAG9E,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAAC+E,eAAe,CAAEC,kBAAkB,CAAC,CAAGhF,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACiF,eAAe,CAAEC,kBAAkB,CAAC,CAAGlF,QAAQ,CAAC,KAAK,CAAC,CAC7D;AACA;AACA,KAAM,CAACmF,MAAM,CAAEC,SAAS,CAAC,CAAGpF,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAAEqF,iBAAiB,CAAEC,cAAc,CAAEC,iBAAkB,CAAC,CAAG7D,WAAW,CAAE8D,KAAU,EAAKA,KAAK,CAAC,CACnG,KAAM,CACL;AACAC,OAAO,CACPC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,MAAM,CACNC,SAAS,CACTC,UAAU,CACVC,aAAa,CACbC,cAAc,CACdC,WAAW,CACXC,kBAAkB,CAClBC,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,UAAU,CACVC,sBAAsB,CACtBC,eAAe,CACfC,aAAa,CACbC,UAAU,CACVC,YAAY,CACZC,yBAAyB,CACzBC,4BAA4B,CAC5BC,yBAAyB,CACzBC,qBAAqB,CACrBC,sBAAsB,CACtBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,eAAe,CACfC,kBAAkB,CAClBC,eAAe,CACfC,kBAAkB,CAClBC,qBAAqB,CACrBC,YAAY,CACZC,eAAe,CACfC,iBAAiB,CACjBC,oBAAoB,CACpBC,4BAA4B,CAC5BC,oBAAoB,CACpBC,kBAAkB,CAClBC,qBAAqB,CACrBC,WAAW,CACXC,cAAc,CACdC,WAAW,CACXC,iBAAiB,CACjBC,oBAAoB,CACpBC,gBAAgB,CAChBC,yBAAyB,CACzBC,gBAAgB,CAChBC,OAAO,CACPC,oBAAoB,CACpBC,QAAQ,CACRC,WAAW,CACXC,iBAAiB,CACjBC,aAAa,CACbC,gBAAgB,CAClBC,mBAAmB,CACjBC,oBAAoB,CACpBC,uBAAuB,CACvBC,gBAAgB,CAChBC,mBAAmB,CACnBC,YAAY,CACZC,eACD,CAAC,CAAGxI,cAAc,CAAEyE,KAAU,EAAKA,KAAK,CAAC,CACzC,KAAM,CAAAgE,UAAU,CAAGzI,cAAc,CAAEyE,KAAK,EAAKA,KAAK,CAACgE,UAAU,CAAC,CAE9DzJ,SAAS,CAAC,IAAM,CACfmE,qBAAqB,CAAC,KAAK,CAAC,CAC5BE,8BAA8B,CAAC,KAAK,CAAC,CACrC0C,4BAA4B,CAAC,KAAK,CAAC,CACnCxC,kBAAkB,CAAC,KAAK,CAAC,CACzBE,uBAAuB,CAAC,KAAK,CAAC,CAC9BE,gBAAgB,CAAC,KAAK,CAAC,CACvBE,gBAAgB,CAAC,KAAK,CAAC,CACvB6B,eAAe,CAAC,KAAK,CAAC,CACtBC,aAAa,CAAC,KAAK,CAAC,CACpByC,uBAAuB,CAAC,KAAK,CAAC,CAC/B,CAAC,CAAE,CAAC1F,gBAAgB,CAAEkF,oBAAoB,CAAC,CAAC,CAC5C;AACA;AACA,KAAM,CAAAc,oBAAoB,CAAGA,CAAA,GAAM,CAClC,GACChG,gBAAgB,GAAK,SAAS,EAC9BA,gBAAgB,GAAK,SAAS,EAC9BkF,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,SAAS,CACjC,CACD7B,4BAA4B,CAAC,CAACD,yBAAyB,CAAC,CACzD,CAAC,IACI,IAAIpD,gBAAgB,GAAK,WAAW,CACzC,CACCW,8BAA8B,CAAC,CAACD,2BAA2B,CAAC,CAC7D,CAAC,IACI,CACJD,qBAAqB,CAAC,CAACD,kBAAkB,CAAC,CAC3C,CACD,CAAC,CACD,KAAM,CAAAyF,qBAAqB,CAAGA,CAAA,GAAM,CACnCC,YAAY,CAAC,KAAK,CAAC,CACnBrF,kBAAkB,CAAC,CAACD,WAAW,CAAC,CACjC,CAAC,CACD,KAAM,CAAAuF,kBAAkB,CAAGA,CAAA,GAAM,CAChC;AACAnD,eAAe,CAAC,IAAI,CAAC,CAAE;AACvBoD,UAAU,CAAC,IAAM,CAChBpD,eAAe,CAAC,IAAI,CAAC,CAAE;AACxB,CAAC,CAAE,CAAC,CAAC,CACN,CAAC,CAED,KAAM,CAAAqD,gBAAgB,CAAGA,CAAA,GACzB,CACCpD,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,CACD,KAAM,CAAAqD,qBAAqB,CAAGA,CAAA,GAC9B,CACCV,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED;AACA;AACA;AACA;AACA;AAEA;AACA;AAGA,KAAM,CAAAW,qBAAqB,CAAGA,CAAA,GAAM,CACnC;AACA;AACA;AACA;AACA;AACA;AACA/C,sBAAsB,CAAC,GAAG,CAAC,CAC3B;AACA;AACA;AACA;AACAO,kBAAkB,CAAC,IAAI,CAAC,CACxByC,iBAAiB,CAAC,KAAK,CAAC,CACxBnD,4BAA4B,CAAC,KAAK,CAAC,CACpC,CAAC,CAED/G,SAAS,CAAC,IAAM,CAEf,KAAM,CAAAmK,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI5E,cAAc,EAAI,EAAE,EAAIA,cAAc,EAAI,IAAI,CAAE,KAAA6E,qBAAA,CACrD;AACA,KAAM,CAAAC,eAAe,CAAGrC,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAGK,WAAW,CAAG,CAAC,CAAC,CAE/D,GAAIgC,eAAe,SAAfA,eAAe,YAAAD,qBAAA,CAAfC,eAAe,CAAEC,MAAM,UAAAF,qBAAA,WAAvBA,qBAAA,CAAyBG,QAAQ,CAAE,CACrC;AACA,KAAM,CAAAC,cAAc,CAAGH,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,EAChEJ,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAErDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAEJ,cAAc,CAAE,WAAW,CAAEH,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CAAC,CAElH,GAAID,cAAc,CAAE,CACrB5C,eAAe,CAAC,QAAQ,CAAC,CACzBQ,cAAc,CAAC,IAAI,CAAC,CACpBF,qBAAqB,CAAC,IAAI,CAAC,CAC3B;AACAe,gBAAgB,CAACoB,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,EAAI,EAAE,CAAC,CAChE3C,oBAAoB,CAACuC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU,EAAIR,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACO,UAAU,EAAI,EAAE,CAAC,CACpHrB,UAAU,CAACY,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,EAAI,EAAE,CAAC,CAE1DE,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAE,CAC5DjD,YAAY,CAAE,QAAQ,CACtBQ,WAAW,CAAE,IAAI,CACjBa,aAAa,CAAEqB,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CACvD5C,iBAAiB,CAAEwC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU,CAC7DlC,OAAO,CAAE0B,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAC1C,CAAC,CAAC,CACD,CAAC,IAAM,CACR7C,eAAe,CAAC,SAAS,CAAC,CAC1BQ,cAAc,CAAC,KAAK,CAAC,CACrBF,qBAAqB,CAAC,KAAK,CAAC,CAC5Be,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,oBAAoB,CAAC,EAAE,CAAC,CACxB2B,UAAU,CAAC,EAAE,CAAC,CAEdkB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC,CAC/C,CACF,CAAC,IAAM,KAAAG,kBAAA,CAAAC,qBAAA,CAAAC,iBAAA,CACL;AACA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAxJ,uBAAuB,CAAC6D,cAAc,CAAEgE,YAAY,CAAEC,eAAe,CAAC,CACzF,KAAM,CAAA2B,SAAS,CAAGD,IAAI,SAAJA,IAAI,kBAAAH,kBAAA,CAAJG,IAAI,CAAEE,YAAY,UAAAL,kBAAA,kBAAAC,qBAAA,CAAlBD,kBAAA,CAAoBM,SAAS,UAAAL,qBAAA,iBAA7BA,qBAAA,CAAgC3C,WAAW,CAAG,CAAC,CAAC,CAElE,GAAI8C,SAAS,SAATA,SAAS,YAAAF,iBAAA,CAATE,SAAS,CAAEG,MAAM,UAAAL,iBAAA,WAAjBA,iBAAA,CAAmBM,QAAQ,CAAE,CAClC,KAAM,CAAAf,cAAc,CAAGW,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,EACvDU,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAElD,GAAIF,cAAc,CAAE,CAClB5C,eAAe,CAAC,QAAQ,CAAC,CACzBQ,cAAc,CAAC,IAAI,CAAC,CACpBF,qBAAqB,CAAC,IAAI,CAAC,CAC3B;AACAe,gBAAgB,CAACkC,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,EAAI,EAAE,CAAC,CAC1D3C,oBAAoB,CAACqD,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACV,UAAU,EAAI,EAAE,CAAC,CAChEpB,UAAU,CAAC0B,SAAS,CAACG,MAAM,CAACC,QAAQ,CAACd,QAAQ,EAAI,EAAE,CAAC,CACtD,CAAC,IAAM,CACL7C,eAAe,CAAC,SAAS,CAAC,CAC1BQ,cAAc,CAAC,KAAK,CAAC,CACrBF,qBAAqB,CAAC,KAAK,CAAC,CAC5Be,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,oBAAoB,CAAC,EAAE,CAAC,CACxB2B,UAAU,CAAC,EAAE,CAAC,CAChB,CACC,CAAC,IAAM,CACR7B,eAAe,CAAC,SAAS,CAAC,CAC1BQ,cAAc,CAAC,KAAK,CAAC,CACrBF,qBAAqB,CAAC,KAAK,CAAC,CAC5Be,gBAAgB,CAAC,EAAE,CAAC,CACpBnB,oBAAoB,CAAC,EAAE,CAAC,CACxB2B,UAAU,CAAC,EAAE,CAAC,CACb,CACF,CACC,CACF,CAAC,CACDU,iBAAiB,CAAC,CAAC,CAClB,CAAC,CAAE,CAAC9B,WAAW,CAAEL,oBAAoB,CAAC,CAAC,CAGzC,KAAM,CAAAwD,uBAAuB,CAAIC,OAAoB,EAAK,CACzDA,OAAO,CAACC,eAAe,CAAC,UAAU,CAAC,CACnCD,OAAO,CAACE,KAAK,CAACC,OAAO,CAAG,EAAE,CAC1BH,OAAO,CAACE,KAAK,CAACE,aAAa,CAAG,OAAO,CACtC,CAAC,CACD,KAAM,CAACC,SAAS,CAAElC,YAAY,CAAC,CAAG3J,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAAA8L,iBAAiB,CAAGA,CAAA,GAAM,CAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAM,CAAAC,eAAe,CAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC,CACvE,KAAM,CAAAC,eAAe,CAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC,CACjEhD,mBAAmB,CAAC,KAAK,CAAC,CAEzB;AACAzF,cAAc,CAAC,KAAK,CAAC,CACrBgE,kBAAkB,CAAC,KAAK,CAAC,CACzBC,qBAAqB,EAAI8D,uBAAuB,CAAC9D,qBAAqB,CAAC,CAExEwB,mBAAmB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED;AAEA;AACA,KAAM,CAAAkD,iBAAiB,CAAIC,QAAa,EAAK,CAC5C1B,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEyB,QAAQ,CAAC,CACxCnD,mBAAmB,CAAC,IAAI,CAAC,CAEzBoD,iBAAiB,CAACD,QAAQ,CAAC,CAC3B9D,oBAAoB,CAAEgE,IAAS,EAAMA,IAAI,GAAKF,QAAQ,CAAG,IAAI,CAAGA,QAAS,CAAC,CAE1E;AACA;AACD,CAAC,CAED,KAAM,CAAAG,oBAAoB,CAAGA,CAAA,GAC7B,CACCpD,uBAAuB,CAAC,IAAI,CAAC,CAC7B,CAAC,CACF,KAAM,CAAAkD,iBAAiB,CAAIG,IAAS,EAAK,CACxC9B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAE6B,IAAI,CAAC,CAExD,GAAIA,IAAI,GAAK,CAAC,CAAE,CACf;AACA9B,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC,CAC9ChD,eAAe,CAAC,SAAS,CAAC,CAC1BE,oBAAoB,CAAC,EAAE,CAAC,CACxBmB,gBAAgB,CAAC,EAAE,CAAC,CACpBQ,UAAU,CAAC,EAAE,CAAC,CACdrB,cAAc,CAAC,KAAK,CAAC,CACrBF,qBAAqB,CAAC,KAAK,CAAC,CAE5B;AACA,KAAM,CAAAwE,qBAAqB,CAAG,CAC7BC,QAAQ,CAAE,SAAS,CACnBlC,QAAQ,CAAE,EAAE,CACZmC,WAAW,CAAE,EAAE,CACf/B,UAAU,CAAE,EAAE,CACdgC,EAAE,CAAE,EACL,CAAC,CACD9E,4BAA4B,CAAC2E,qBAAqB,CAAC,CAEpD,CAAC,IAAM,IAAID,IAAI,GAAK,CAAC,CAAE,KAAAK,sBAAA,CAAAC,sBAAA,CACtB;AACApC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC,CAC7ChD,eAAe,CAAC,QAAQ,CAAC,CACzBQ,cAAc,CAAC,IAAI,CAAC,CACpBF,qBAAqB,CAAC,IAAI,CAAC,CAE3B;AACA,KAAM,CAAAmC,eAAe,CAAGrC,oBAAoB,SAApBA,oBAAoB,iBAApBA,oBAAoB,CAAGK,WAAW,CAAG,CAAC,CAAC,CAC/DsC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAEP,eAAe,CAAC,CAEzD,GAAIA,eAAe,SAAfA,eAAe,YAAAyC,sBAAA,CAAfzC,eAAe,CAAEC,MAAM,UAAAwC,sBAAA,YAAAC,sBAAA,CAAvBD,sBAAA,CAAyBvC,QAAQ,UAAAwC,sBAAA,WAAjCA,sBAAA,CAAmCtC,QAAQ,CAAE,CAChD,KAAM,CAAA4B,QAAQ,CAAGhC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACE,QAAQ,CACzD,KAAM,CAAAK,UAAU,CAAGT,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACM,UAAU,CAE7D;AACA5B,gBAAgB,CAACoD,QAAQ,CAAC,CAC1BvE,oBAAoB,CAACgD,UAAU,EAAI,EAAE,CAAC,CACtCrB,UAAU,CAAC4C,QAAQ,CAAC,CAEpB1B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAE,CACzEyB,QAAQ,CACRvB,UAAU,CACV9B,aAAa,CAAEqD,QAChB,CAAC,CAAC,CACH,CAAC,IAAM,CACN1B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAC/D,CACD,CACD,CAAC,CACD,KAAM,CAAAoC,sBAAsB,CAAGA,CAACC,KAAU,CAAEC,IAAS,GAAK,CACzD,KAAM,CAAAR,qBAAqB,CAAG,CAC7BC,QAAQ,CAAEhF,YAAY,CACtB8C,QAAQ,CAAEwC,KAAK,CACfL,WAAW,CAAE,EAAE,CACf/B,UAAU,CAAEqC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEA,IAAI,CACtBL,EAAE,CAAEI,KAAO;AACZ,CAAC,CAEDlF,4BAA4B,CAAC2E,qBAAqB,CAAC,CACpD,CAAC,CAED,KAAM,CAAAS,oBAAoB,CAAIC,KAAU,EAAK,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAC5C,KAAM,CAAAC,aAAa,CAAGJ,KAAK,CAACK,MAAM,CAACR,KAAK,CACxCtC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE4C,aAAa,CAAC,CAElD;AACA,KAAM,CAAAE,eAAe,EAAAL,qBAAA,CAAGrF,oBAAoB,CAACK,WAAW,CAAG,CAAC,CAAC,UAAAgF,qBAAA,kBAAAC,sBAAA,CAArCD,qBAAA,CAAuCM,UAAU,UAAAL,sBAAA,iBAAjDA,sBAAA,CAAmDM,IAAI,CAC7EC,SAAc,EAAKA,SAAS,CAACpB,IAAI,GAAK,QACxC,CAAC,CACD,KAAM,CAAAqB,cAAc,CAAGJ,eAAe,SAAfA,eAAe,kBAAAH,qBAAA,CAAfG,eAAe,CAAEK,OAAO,UAAAR,qBAAA,iBAAxBA,qBAAA,CAA0BK,IAAI,CACnDI,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKT,aAChC,CAAC,CAED7C,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEkD,cAAc,CAAC,CAC/CnD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE8C,eAAe,CAAC,CAEjD;AACAzE,gBAAgB,CAACuE,aAAa,CAAC,CAC/B1F,oBAAoB,CAAC,CAAAgG,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEZ,IAAI,GAAIM,aAAa,CAAC,CAAE;AAC7D/D,UAAU,CAAC+D,aAAa,CAAC,CACzB5F,eAAe,CAAC,QAAQ,CAAC,CAEzB;AACAoF,sBAAsB,CAACQ,aAAa,CAAEM,cAAc,CAAC,CAErD;AACA5E,mBAAmB,CAAC,IAAI,CAAC,CAEzByB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAE,CACnD5B,aAAa,CAAEwE,aAAa,CAC5B3F,iBAAiB,CAAE,CAAAiG,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEZ,IAAI,GAAIM,aAAa,CACxD7E,OAAO,CAAE6E,aAAa,CACtB7F,YAAY,CAAE,QACf,CAAC,CAAC,CACH,CAAC,CAED,KAAM,CAAAuG,sBAAsB,CAAGA,CAAA,GAAM,CACpCtE,YAAY,CAAC,KAAK,CAAC,CACnBnF,uBAAuB,CAAC,IAAI,CAAC,CAC7B;AACA,mBACCnC,KAAA,CAAAF,SAAA,EAAA+L,QAAA,EACE3J,oBAAoB,EAAIb,WAAW,eACnCzB,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACvB,gBAAgB,EAChB8D,uBAAuB,CAAEA,uBAAwB,CACjDhB,cAAc,CAAEA,cAAe,CAC/B,CAAC,CACE,CACL,CAAC,GAEH,EAAE,CAAC,CAEL,CAAC,CAED,KAAM,CAAA2K,eAAe,CAAGA,CAAA,GAAM,CAC7BvJ,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAAE;AACnC,CAAC,CACD,KAAM,CAAAyJ,eAAe,CAAGA,CAAA,GAAM,CAC7B1J,gBAAgB,CAAC,CAACD,aAAa,CAAC,CAAE;AACnC,CAAC,CAED,KAAM,CAAA4J,WAAW,CAAGA,CAAA,GAAM,CACzBjJ,SAAS,CAAC,KAAK,CAAC,CAAE;AAClB5B,cAAc,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAA8K,uBAAuB,CAAIrD,IAAS,EAAK,CAAC,CAAC,CACjD,GAAI,CAAC9F,MAAM,CAAE,MAAO,KAAI,CACxB,KAAM,CAAAoJ,kBAAkB,CAAIC,MAAe,EAAK,CAC/ClI,iBAAiB,CAACkI,MAAM,CAAC,CAC1B,CAAC,CAED,mBACC;AACAnM,KAAA,QACC2L,EAAE,CAAC,mBAAmB,CACtBS,SAAS,CAAC,mBAAmB,CAAAP,QAAA,eAE7B7L,KAAA,QAAKoM,SAAS,CAAC,eAAe,CAAAP,QAAA,eAC7B7L,KAAA,QAAKoM,SAAS,CAAC,qBAAqB,CAAAP,QAAA,eACnCjM,IAAA,QAAKwM,SAAS,CAAC,aAAa,CAAAP,QAAA,CAAElK,SAAS,CAAC,QAAQ,CAAC,CAAM,CAAC,cACxD/B,IAAA,CAAC7B,UAAU,EACVsO,IAAI,CAAC,OAAO,CACZ,aAAY1K,SAAS,CAAC,OAAO,CAAE,CAC/B2K,OAAO,CAAEN,WAAY,CAAAH,QAAA,cAErBjM,IAAA,CAAC1B,SAAS,GAAE,CAAC,CACF,CAAC,EACT,CAAC,CAGLoG,UAAU,eACV1E,IAAA,CAAAE,SAAA,EAAA+L,QAAA,cACCjM,IAAA,CAACH,aAAa,GAAC,CAAC,CACf,CACF,CAEAoH,oBAAoB,eACpBjH,IAAA,CAACL,gBAAgB,GAAC,CAElB,CACAiK,SAAS,eACT5J,IAAA,CAAAE,SAAA,EAAA+L,QAAA,cACC7L,KAAA,QAAKoM,SAAS,CAAC,gBAAgB,CAAAP,QAAA,EAC7B,CAACzK,gBAAgB,GAAK,SAAS,EAAIkF,oBAAoB,GAAK,SAAS,gBACrEtG,KAAA,CAACnC,GAAG,EACHuO,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAE7C,iBAAkB,CAC3B8C,EAAE,CAAE,CAACC,MAAM,CAAC,SAAS,CAAE,CAAAX,QAAA,eAEvBjM,IAAA,CAAC9B,UAAU,EACVsO,SAAS,CAAC,qBAAqB,CAC/BG,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAZ,QAAA,CAExBlK,SAAS,CAAC,kBAAkB,CAAC,CACnB,CAAC,cACb/B,IAAA,SACCwM,SAAS,CAAC,qBAAqB,CAC/BM,uBAAuB,CAAE,CAAEC,MAAM,CAAE3N,QAAS,CAAE,CAC9CqK,KAAK,CAAE,CAAEjG,OAAO,CAAE,KAAK,CAAEwJ,WAAW,CAAE,MAAO,CAAE,CAC/C,CAAC,EACE,CACL,CACA,CAACxL,gBAAgB,GAAK,SAAS,EAAIkF,oBAAoB,GAAK,SAAS,gBACrEtG,KAAA,QACC;AACAqJ,KAAK,CAAE,CACNwD,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpB1J,OAAO,CAAE,UAAU,CACnB2J,UAAU,CAAE,SAAS,CACrBC,aAAa,CAAE,MAChB,CAAE,CAAAnB,QAAA,eASF7L,KAAA,QAAKqJ,KAAK,CAAE,CAAE4D,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,YAAY,CAAEC,UAAU,CAAE,QAAS,CAAE,CAACf,SAAS,CAAC,cAAc,CAAAP,QAAA,eAC5GjM,IAAA,SACCyJ,KAAK,CAAE,CACN0D,UAAU,CAAE,yBAAyB,CACrCD,YAAY,CAAE,OAAO,CACrB1J,OAAO,CAAE,SACV,CAAE,CAAAyI,QAAA,cAEFjM,IAAA,CAACT,gBAAgB,EAChBkK,KAAK,CAAE,CACNoD,KAAK,CAAE,qBAAqB,CAC5BrM,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MACP;AACA;AACD,CAAE,CACF,CAAC,CACG,CAAC,cACPP,IAAA,CAAC9B,UAAU,EAACyO,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAEW,UAAU,CAAE,KAAM,CAAE,CAAAvB,QAAA,CAAElK,SAAS,CAAC,iBAAiB,CAAC,CAAa,CAAC,EAChG,CAAC,cAEN3B,KAAA,QAAKqJ,KAAK,CAAE,CAAE4D,OAAO,CAAE,MAAM,CAAEI,GAAG,CAAE,KAAK,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAzB,QAAA,eAE9DjM,IAAA,QACC0M,OAAO,CAAEA,CAAA,GAAMxC,iBAAiB,CAAC,CAAC,CAAE,CACpCT,KAAK,CAAE,CACN4D,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpB/M,MAAM,CAAE,MAAM,CACd0M,YAAY,CAAE,KAAK,CACnBN,MAAM,CAAE,SAAS,CACjBzI,eAAe,CACdsB,YAAY,GAAK,SAAS,CAAG,yBAAyB,CAAG,yBAAyB,CAEnFkI,MAAM,CAAElI,YAAY,GAAK,SAAS,CAAG,+BAA+B,CAAG,uBAAuB,CAC9FlF,KAAK,CAAE,MACR,CAAE,CAAA0L,QAAA,cASFjM,IAAA,CAAC9B,UAAU,EAACyO,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAErJ,OAAO,CAAE,OAAO,CAAEoK,QAAQ,CAAE,iBAAkB,CAAE,CAAA3B,QAAA,CAClFlK,SAAS,CAAC,eAAe,CAAC,CAChB,CAAC,CACT,CAAC,cAGN/B,IAAA,QACC0M,OAAO,CAAEA,CAAA,GAAMxC,iBAAiB,CAAC,CAAC,CAAE,CACpCT,KAAK,CAAE,CACN4D,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,QAAQ,CACpB/M,MAAM,CAAE,MAAM,CACd0M,YAAY,CAAE,KAAK,CACnBN,MAAM,CAAE,SAAS,CACjBzI,eAAe,CACdsB,YAAY,GAAK,SAAS,CAAG,yBAAyB,CAAG,yBAAyB,CAEnFkI,MAAM,CAAElI,YAAY,GAAK,SAAS,CAAG,+BAA+B,CAAG,uBAAuB,CAC9FlF,KAAK,CAAE,MACR,CAAE,CAAA0L,QAAA,cASFjM,IAAA,CAAC9B,UAAU,EAACyO,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAErJ,OAAO,CAAE,QAAQ,CAAEoK,QAAQ,CAAE,iBAAkB,CAAE,CAAA3B,QAAA,CACnFlK,SAAS,CAAC,cAAc,CAAC,CACf,CAAC,CACT,CAAC,EACF,CAAC,CACLkE,WAAW,eACXjG,IAAA,QAAAiM,QAAA,cACC7L,KAAA,CAACnC,GAAG,EACHuO,SAAS,CAAC,gBAAgB,CAC1BG,EAAE,CAAE,CACHkB,MAAM,CAAE,cAAc,CACtBZ,YAAY,CAAE,KAAK,CACnBC,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,SAAS,CACrBC,aAAa,CAAE,MAChB,CAAE,CAAAnB,QAAA,eAEFjM,IAAA,CAAC9B,UAAU,EAACyO,EAAE,CAAE,CAAGnJ,OAAO,CAAE,KAAK,CAAEqJ,KAAK,CAAE,UAAW,CAAE,CAAAZ,QAAA,CACrDlK,SAAS,CAAC,eAAe,CAAC,CAChB,CAAC,CACZ,CAAC,CAAA+L,sBAAA,CAAAC,sBAAA,GAAM,CACP;AACA,KAAM,CAAAvC,eAAe,CAAG1F,oBAAoB,SAApBA,oBAAoB,kBAAAgI,sBAAA,CAApBhI,oBAAoB,CAAGK,WAAW,CAAG,CAAC,CAAC,UAAA2H,sBAAA,kBAAAC,sBAAA,CAAvCD,sBAAA,CAAyCrC,UAAU,UAAAsC,sBAAA,iBAAnDA,sBAAA,CAAqDrC,IAAI,CAC/EC,SAAc,EAAKA,SAAS,CAACpB,IAAI,GAAK,QACxC,CAAC,CACD,MAAO,CAAAiB,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEK,OAAO,cAC9B7L,IAAA,CAAAE,SAAA,EAAA+L,QAAA,cACC7L,KAAA,CAAChC,MACA;AAAA,EACA2M,KAAK,CAAE,CAAC,CAAAiD,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,GAAM,CACZ;AACA,KAAM,CAAAC,cAAc,EAAAR,sBAAA,CAAGlI,oBAAoB,CAACK,WAAW,CAAG,CAAC,CAAC,UAAA6H,sBAAA,kBAAAC,sBAAA,CAArCD,sBAAA,CAAuC5F,MAAM,UAAA6F,sBAAA,kBAAAC,sBAAA,CAA7CD,sBAAA,CAA+C5F,QAAQ,UAAA6F,sBAAA,iBAAvDA,sBAAA,CAAyD3F,QAAQ,CACxF,GAAIiG,cAAc,EAAIhD,eAAe,SAAfA,eAAe,YAAA2C,sBAAA,CAAf3C,eAAe,CAAEK,OAAO,UAAAsC,sBAAA,WAAxBA,sBAAA,CAA0BM,IAAI,CAAE3C,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKyC,cAAc,CAAC,CAAE,CACtG,MAAO,CAAAA,cAAc,CACpB,CAEA;AACA,GAAI/H,OAAO,EAAI+E,eAAe,SAAfA,eAAe,YAAA4C,sBAAA,CAAf5C,eAAe,CAAEK,OAAO,UAAAuC,sBAAA,WAAxBA,sBAAA,CAA0BK,IAAI,CAAE3C,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKtF,OAAO,CAAC,CAAE,CACxF,MAAO,CAAAA,OAAO,CACb,CAEA;AACA,GAAIK,aAAa,EAAI0E,eAAe,SAAfA,eAAe,YAAA6C,sBAAA,CAAf7C,eAAe,CAAEK,OAAO,UAAAwC,sBAAA,WAAxBA,sBAAA,CAA0BI,IAAI,CAAE3C,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKjF,aAAa,CAAC,CAAE,CACpG,MAAO,CAAAA,aAAa,CACnB,CACA;AACA,GAAInB,iBAAiB,EAAI6F,eAAe,SAAfA,eAAe,YAAA8C,sBAAA,CAAf9C,eAAe,CAAEK,OAAO,UAAAyC,sBAAA,WAAxBA,sBAAA,CAA0BI,MAAM,CAAE,CAC5D,KAAM,CAAAC,KAAK,CAAGnD,eAAe,CAACK,OAAO,CAACH,IAAI,CAAEI,MAAW,EAAKA,MAAM,CAACd,IAAI,GAAKrF,iBAAiB,CAAC,CAC9F,GAAIgJ,KAAK,CAAE,MAAO,CAAAA,KAAK,CAAC5C,EAAE,CACzB,CACA;AACA,GAAIyC,cAAc,EAAIhD,eAAe,SAAfA,eAAe,YAAA+C,sBAAA,CAAf/C,eAAe,CAAEK,OAAO,UAAA0C,sBAAA,WAAxBA,sBAAA,CAA0BG,MAAM,CAAE,CACzD,KAAM,CAAAC,KAAK,CAAGnD,eAAe,CAACK,OAAO,CAACH,IAAI,CAAEI,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKyC,cAAc,CAAC,CACzF,GAAIG,KAAK,CAAE,MAAO,CAAAH,cAAc,CAC/B,CACA;AACA,MAAO,EAAE,CACX,CAAC,EAAE,CAAE,CACLI,QAAQ,CAAE3D,oBAAqB,CAC/B4D,YAAY,MACZpF,KAAK,CAAE,CAAElJ,KAAK,CAAE,MAAO,CAAE,CACzBoM,EAAE,CAAE,CACF,oBAAoB,CAAE,CACvBnM,MAAM,CAAE,MAAM,CACdD,KAAK,CAAE,MAAM,CACbuO,GAAG,CAAE,MACJ,CACF,CAAE,CACFC,WAAW,CAAGC,QAAQ,EAAK,KAAAC,sBAAA,CAC1B;AACA,GAAI,CAACD,QAAQ,CAAE,MAAO,CAAAjN,SAAS,CAAC,kBAAkB,CAAC,CAEnD,KAAM,CAAAmN,GAAG,CAAG1D,eAAe,SAAfA,eAAe,kBAAAyD,sBAAA,CAAfzD,eAAe,CAAEK,OAAO,UAAAoD,sBAAA,iBAAxBA,sBAAA,CAA0BvD,IAAI,CAAEI,MAAW,EAAKA,MAAM,CAACC,EAAE,GAAKiD,QAAQ,CAAC,CACnF,MAAO,CAAAE,GAAG,SAAHA,GAAG,iBAAHA,GAAG,CAAElE,IAAI,GAAIjJ,SAAS,CAAC,kBAAkB,CAAC,CAClD,CAAE,CAAAkK,QAAA,eAEFjM,IAAA,CAAC3B,QAAQ,EAAC0M,KAAK,CAAC,EAAE,CAACoE,QAAQ,MAAAlD,QAAA,CACzBlK,SAAS,CAAC,kBAAkB,CAAC,CACrB,CAAC,CACVyJ,eAAe,CAACK,OAAO,CAACuD,GAAG,CAC1B,CAACtD,MAAW,CAAEuD,WAAmB,gBAClCrP,IAAA,CAAC3B,QAAQ,EAAmB0M,KAAK,CAAEe,MAAM,CAACC,EAAG,CAAAE,QAAA,CAC1CH,MAAM,CAACd,IAAI,EADCqE,WAEL,CAEX,CAAC,EACM,CAAC,CAER,CAAC,CACA,IAAI,CACT,CAAC,EAAE,CAAC,CAAE,GAAG,EAEL,CAAC,CACF,CACL,EACG,CACL,CACA7N,gBAAgB,GAAK,SAAS,EAAIkF,oBAAoB,GAAK,SAAS,cACpEtG,KAAA,CAACpC,MAAM,EACNwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAE/E,kBAAoB;AAAA,CAAAsE,QAAA,eAE7BjM,IAAA,SACCwM,SAAS,CAAC,gBAAgB,CAC1BM,uBAAuB,CAAE,CAAEC,MAAM,CAAE7N,WAAY,CAAE,CACjD,CAAC,cACFc,IAAA,CAAC9B,UAAU,EACVyO,EAAE,CAAE,CAACa,UAAU,CAAE,gBAAgB,CAAE,CAAAvB,QAAA,CAElClK,SAAS,CAAC,SAAS,CAAC,CACV,CAAC,EACN,CAAC,CAET,EACA,CACCP,gBAAgB,GAAK,WAAW,eACjCpB,KAAA,CAAAF,SAAA,EAAA+L,QAAA,eACAjM,IAAA,CAAChC,MAAM,EACLwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAE5E,qBAAsB,CAEjCwH,SAAS,cAAEtP,IAAA,CAACzB,kBAAkB,GAAE,CAAE,CAAA0N,QAAA,CAE/BlK,SAAS,CAAC,OAAO,CAAC,CACb,CAAC,cAET/B,IAAA,CAAChC,MAAM,EACLwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAE7E,gBAAiB,CAC5ByH,SAAS,cAAEtP,IAAA,SACV8M,uBAAuB,CAAE,CAAEC,MAAM,CAAE9N,QAAS,CAAE,CAC9CwK,KAAK,CAAE,CAAEjJ,MAAM,CAAE,MAAO,CAAE,CAC1B,CAAE,CAAAyL,QAAA,CAEAlK,SAAS,CAAC,kBAAkB,CAAC,CACvB,CAAC,EACP,CAEH,CAGAoF,gBAAgB,eAChBnH,IAAA,CAAAE,SAAA,EAAA+L,QAAA,cACCjM,IAAA,CAACJ,WAAW,GAAC,CAAC,CACb,CAEF,cAIDI,IAAA,CAAChC,MAAM,EACNwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAElF,oBAAqB,CAC9B8H,SAAS,cAAEtP,IAAA,SACV8M,uBAAuB,CAAE,CAAEC,MAAM,CAAE1N,UAAW,CAAE,CAChDoK,KAAK,CAAE,CAAEjJ,MAAM,CAAE,MAAO,CAAE,CAC1B,CAAE,CAAAyL,QAAA,CAEFlK,SAAS,CAAC,QAAQ,CAAC,CACb,CAAC,CAERP,gBAAgB,EAAI,WAAW,eAC/BpB,KAAA,SAAA6L,QAAA,eACCjM,IAAA,CAAChC,MAAM,EACNwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEV,sBAAuB,CAChCsD,SAAS,cACRtP,IAAA,SACC8M,uBAAuB,CAAE,CAAEC,MAAM,CAAE9N,QAAS,CAAE,CAC9CwK,KAAK,CAAE,CAAEjJ,MAAM,CAAE,MAAO,CAAE,CAC1B,CACD,CAAAyL,QAAA,CAEAlK,SAAS,CAAC,UAAU,CAAC,CACf,CAAC,CAET,CAAC,CAAC,SAAS,CAAE,cAAc,CAAC,CAACwN,QAAQ,CAAC/N,gBAAgB,CAAC,EACtDA,gBAAgB,GAAK,MAAM,EAAI,CAAC,SAAS,CAAE,cAAc,CAAC,CAAC+N,QAAQ,CAAC7I,oBAAoB,CAAE,gBACzF1G,IAAA,CAAChC,MAAM,EACNwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEjF,qBAAsB,CAC/B6H,SAAS,cACRtP,IAAA,SACC8M,uBAAuB,CAAE,CAAEC,MAAM,CAAE5N,OAAQ,CAAE,CAC7CsK,KAAK,CAAE,CAAEjJ,MAAM,CAAE,MAAO,CAAE,CAC1B,CAEH;AACA;AACA;AAAA,CAAAyL,QAAA,CAEClK,SAAS,CAAC,SAAS,CAAC,CACb,CACR,EACG,CACN,CA8BAP,gBAAgB,GAAK,WAAW,eAChCxB,IAAA,CAAChC,MAAM,EACNwO,SAAS,CAAC,kBAAkB,CAC5BE,OAAO,CAAEpC,oBAAqB,CAC9BgF,SAAS,cAAEtP,IAAA,SACV8M,uBAAuB,CAAE,CAAEC,MAAM,CAAE/N,SAAU,CAAE,CAC/CyK,KAAK,CAAE,CAAEjJ,MAAM,CAAE,MAAO,CAAE,CAC1B,CAAE,CAAAyL,QAAA,CAEFlK,SAAS,CAAC,UAAU,CAAC,CACf,CACR,EACG,CAAC,CACL,CACF,EACG,CAAC,CACL4C,YAAY,eACZ3E,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACjB,eAAe,GAAE,CAAC,CACf,CACL,CAEAiD,kBAAkB,GAAKR,gBAAgB,GAAK,QAAQ,EAAIkF,oBAAoB,GAAK,QAAQ,CAAC,cAC1F1G,IAAA,CAACpB,gBAAgB,EAChBqD,qBAAqB,CAAEA,qBAAsB,CAC7CtB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,SAAS,CAAEA,SAAU,CACrBC,YAAY,CAAEA,YAAa,CAC3BC,QAAQ,CAAEA,QAAS,CACnBC,WAAW,CAAEA,WAAY,CACzBC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEA,UAAW,CACvBO,mBAAmB,CAAEA,mBAAoB,CACzC,CAAC,CAEFG,kBAAkB,eACjBhC,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACxB,cAAc,EACd6C,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEA,UAAW,CACvBW,qBAAqB,CAAEA,qBACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,CACA,CAAC,CACE,CAEN,CAEA2C,yBAAyB,GACzBpD,gBAAgB,GAAK,SAAS,EAC9BA,gBAAgB,GAAK,SAAS,EAC9BkF,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,SAAS,CAAC,cACnC1G,IAAA,CAACV,qBAAqB,EACrBuF,4BAA4B,CAAEA,4BAA6B,CAC3DlE,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,SAAS,CAAEA,SAAU,CACrBC,YAAY,CAAEA,YAAa,CAC3BC,QAAQ,CAAEA,QAAS,CACnBC,WAAW,CAAEA,WAAY,CACzBC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEA,UAAW,CACvB,CAAC,CAEF,EACA,CAIHY,2BAA2B,EACxBV,gBAAgB,GAAK,WACrB,cACAxB,IAAA,CAACN,uBAAuB,EACvByC,8BAA8B,CAAEA,8BAA+B,CAC/DxB,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,SAAS,CAAEA,SAAU,CACrBC,YAAY,CAAEA,YAAa,CAC3BC,QAAQ,CAAEA,QAAS,CACnBC,WAAW,CAAEA,WAAY,CACzBC,YAAY,CAAEA,YAAa,CAC3BC,eAAe,CAAEA,eAAgB,CACjCC,WAAW,CAAEA,WAAY,CACzBC,cAAc,CAAEA,cAAe,CAC/BC,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEA,UAAW,CACvB,CAAC,CAEF,EACA,CAKAc,WAAW,eACXpC,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACtB,eAAe,EACf2D,kBAAkB,CAAEA,kBAAmB,CACvCb,gBAAgB,CAAEA,gBAAiB,CACnCgO,cAAc,CAAElD,kBAAmB,CACnC5L,WAAW,CAAEA,WAAY,CACzBa,cAAc,CAAEA,cAAe,CAC/BqB,QAAQ,CAAEA,QAAS,CACnB8E,YAAY,CAAEA,YAAa,CAC3B,CAAC,CACE,CACL,CACApF,oBAAoB,EAAIb,WAAW,eACnCzB,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACvB,gBAAgB,EAChB8D,uBAAuB,CAAEA,uBAAwB,CACjDhB,cAAc,CAAEA,cAAe,CAC/BmG,YAAY,CAAEA,YAAa,CAC3B7F,mBAAmB,CAAEA,mBAAoB,CACzC,CAAC,CACE,CACL,CACAa,aAAa,eACb1C,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACrB,SAAS,GAAE,CAAC,CACT,CACL,CACA6D,aAAa,eACbxC,IAAA,CAAC/B,GAAG,EAAAgO,QAAA,cACHjM,IAAA,CAACnB,iBAAiB,EAAC2C,gBAAgB,CAAEA,gBAAiB,CAAE,CAAC,CACrD,CACL,EACG,CAEL;AAAA,EAEF,CAAC,CAED,cAAe,CAAAnB,UAAU,CACzB,QAAS,CAAAoP,eAAeA,CAACC,IAAY,CAAE,CACtC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAAC,eAAeA,CAACF,IAAY,CAAE,CACtC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAAE,kBAAkBA,CAACH,IAAY,CAAE,CACzC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAA3K,sBAAsBA,CAAC0K,IAAY,CAAE,CAC7C,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAAG,iBAAiBA,CAACJ,IAAY,CAAE,CACxC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAAI,eAAeA,CAACL,IAAY,CAAE,CACtC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAAK,qBAAqBA,CAAC9K,oBAAyB,CAAE,CACzD,KAAM,IAAI,CAAAyK,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAApK,kBAAkBA,CAACmK,IAAa,CAAE,CAC1C,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C,CAEA,QAAS,CAAA3H,iBAAiBA,CAAC0H,IAAa,CAAE,CACzC,KAAM,IAAI,CAAAC,KAAK,CAAC,2BAA2B,CAAC,CAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}